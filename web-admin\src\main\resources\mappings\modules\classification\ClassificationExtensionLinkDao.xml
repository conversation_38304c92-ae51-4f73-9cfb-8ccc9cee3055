<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.classification.dao.ClassificationExtensionLinkDao">
	
	<!-- 根据分类编码查询拓展链接列表 -->
	<select id="findByClassificationCode" resultType="ClassificationExtensionLink">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		WHERE a.classification_code = #{classificationCode}
		ORDER BY a.sort_order ASC, a.id ASC
	</select>
	
	<!-- 根据分类编码删除拓展链接 -->
	<delete id="deleteByClassificationCode">
		DELETE FROM classification_extension_link 
		WHERE classification_code = #{classificationCode}
	</delete>
	
</mapper>
