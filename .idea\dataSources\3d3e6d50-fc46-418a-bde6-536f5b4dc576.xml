<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@172.16.50.70">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <ServerVersion>5.7.26</ServerVersion>
    </root>
    <collation id="2" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="4" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="6" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="8" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="10" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="11" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="12" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="13" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="14" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="15" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="16" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="17" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="18" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="19" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="20" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="21" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="22" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="23" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="24" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="25" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="27" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="29" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="31" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="33" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="35" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="39" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="45" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="47" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="48" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="49" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="50" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="52" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="54" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="56" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="58" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="59" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="60" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="61" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="62" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="63" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="64" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="65" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="66" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="67" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="68" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="69" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="70" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="71" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="72" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="73" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="74" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="75" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="76" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="77" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="78" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="79" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="80" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="81" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="82" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="83" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="85" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="86" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="87" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="111" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="112" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="113" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="114" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="115" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="116" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="118" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="119" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="120" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="121" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="122" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="123" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="124" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="125" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="126" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="127" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="128" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="129" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="130" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="131" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="132" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="133" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="134" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="135" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="136" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="137" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="138" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="139" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="140" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="141" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="142" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="143" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="144" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="145" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="146" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="147" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="148" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="149" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="150" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="151" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="152" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="153" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="154" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="155" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="156" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="157" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="158" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="159" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="160" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="161" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="162" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="163" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="164" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="165" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="166" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="167" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="168" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="169" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="170" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="171" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="172" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="173" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="174" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="175" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="176" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="177" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="178" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="179" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="180" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="181" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="182" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="183" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="184" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="185" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="186" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="187" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="188" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="189" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="190" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="191" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="192" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="193" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="194" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="195" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="196" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="197" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="198" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="199" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="200" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="201" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="202" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="203" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="204" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="205" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="206" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="207" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="208" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="209" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="210" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="211" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="212" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="213" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="214" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="215" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="216" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="217" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="218" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="219" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="220" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="221" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="222" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="223" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <schema id="224" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="advertising">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="226" parent="1" name="advertising2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="227" parent="1" name="amazon-mws">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="228" parent="1" name="asinking-temp">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="229" parent="1" name="bbs">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="230" parent="1" name="bbs-pro">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="231" parent="1" name="bbs-pro2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="232" parent="1" name="bbs-pro3">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="233" parent="1" name="bbs-test">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="234" parent="1" name="crawl_data">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="235" parent="1" name="demo">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="236" parent="1" name="jymx-test">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="237" parent="1" name="lenkor-asinking">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="238" parent="1" name="lenkor-common">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="239" parent="1" name="lenkor-ebay">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="240" parent="1" name="lenkor-ebay-ad-app">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="241" parent="1" name="lenkor-erp">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="242" parent="1" name="lenkor-kingdee">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="243" parent="1" name="lenkor-nacos">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="244" parent="1" name="lenkor-odoo">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="245" parent="1" name="lenkor-seata">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="246" parent="1" name="lenkor-shopify">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="247" parent="1" name="lenkor-sp">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="248" parent="1" name="lenkor-sp-datacleaning">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="249" parent="1" name="lenkor-tongtool-api">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="250" parent="1" name="lenkor-web">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="251" parent="1" name="logistic">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="252" parent="1" name="maxwell">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="253" parent="1" name="mearnic">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="254" parent="1" name="mysql">
      <CollationName>latin1_swedish_ci</CollationName>
    </schema>
    <schema id="255" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="256" parent="1" name="publiccms">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="257" parent="1" name="sys">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="258" parent="1" name="test">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="259" parent="1" name="topdon">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="260" parent="1" name="topdon-dev">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-08-01.08:11:13</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="261" parent="1" name="websupport">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="262" parent="1" name="xxl-job">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="263" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <user id="264" parent="1" name="mysql.session">
      <Host>localhost</Host>
    </user>
    <user id="265" parent="1" name="mysql.sys">
      <Host>localhost</Host>
    </user>
    <user id="266" parent="1" name="root"/>
    <user id="267" parent="1" name="maxwell"/>
    <table id="268" parent="260" name="authorized_dealer">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="269" parent="260" name="authorized_platforms">
      <Comment>授权平台</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="270" parent="260" name="banner">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="271" parent="260" name="cart">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="272" parent="260" name="classification">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="273" parent="260" name="classification_2">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="274" parent="260" name="classification_compare">
      <Comment>分类比较</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="275" parent="260" name="classification_extension_link">
      <Comment>分类拓展链接表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="276" parent="260" name="cooperation_apply">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="277" parent="260" name="data_warehouse">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="278" parent="260" name="email_subscribe">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="279" parent="260" name="file">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="280" parent="260" name="footer_menu">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="281" parent="260" name="information">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="282" parent="260" name="information_group">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="283" parent="260" name="innerapi_user">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="284" parent="260" name="js_gen_table">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="285" parent="260" name="js_gen_table_column">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="286" parent="260" name="js_job_blob_triggers">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="287" parent="260" name="js_job_calendars">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="288" parent="260" name="js_job_cron_triggers">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="289" parent="260" name="js_job_fired_triggers">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="290" parent="260" name="js_job_job_details">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="291" parent="260" name="js_job_locks">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="292" parent="260" name="js_job_paused_trigger_grps">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="293" parent="260" name="js_job_scheduler_state">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="294" parent="260" name="js_job_simple_triggers">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="295" parent="260" name="js_job_simprop_triggers">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="296" parent="260" name="js_job_triggers">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="297" parent="260" name="js_session">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="298" parent="260" name="js_sys_area">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="299" parent="260" name="js_sys_company">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="300" parent="260" name="js_sys_company_office">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="301" parent="260" name="js_sys_config">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="302" parent="260" name="js_sys_dict_data">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="303" parent="260" name="js_sys_dict_type">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="304" parent="260" name="js_sys_employee">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="305" parent="260" name="js_sys_employee_office">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="306" parent="260" name="js_sys_employee_post">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="307" parent="260" name="js_sys_file_entity">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="308" parent="260" name="js_sys_file_upload">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="309" parent="260" name="js_sys_job">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="310" parent="260" name="js_sys_job_log">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="311" parent="260" name="js_sys_lang">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="312" parent="260" name="js_sys_log">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="313" parent="260" name="js_sys_menu">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="314" parent="260" name="js_sys_module">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="315" parent="260" name="js_sys_msg_inner">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="316" parent="260" name="js_sys_msg_inner_record">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="317" parent="260" name="js_sys_msg_push">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="318" parent="260" name="js_sys_msg_pushed">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="319" parent="260" name="js_sys_msg_template">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="320" parent="260" name="js_sys_office">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="321" parent="260" name="js_sys_post">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="322" parent="260" name="js_sys_role">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="323" parent="260" name="js_sys_role_data_scope">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="324" parent="260" name="js_sys_role_menu">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="325" parent="260" name="js_sys_user">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="326" parent="260" name="js_sys_user_data_scope">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="327" parent="260" name="js_sys_user_role">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="328" parent="260" name="menu_click_log">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="329" parent="260" name="news">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="330" parent="260" name="official_website">
      <Comment>官网站点</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="331" parent="260" name="product">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="332" parent="260" name="product_classification">
      <Comment>产品分类信息</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="333" parent="260" name="product_compare">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="334" parent="260" name="product_extension">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="335" parent="260" name="product_lines">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="336" parent="260" name="product_overview">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="337" parent="260" name="product_property">
      <Comment>产品属性表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="338" parent="260" name="product_recommend">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="339" parent="260" name="product_relation">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="340" parent="260" name="products_infomations">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="341" parent="260" name="property_category">
      <Comment>属性种类表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="342" parent="260" name="recruitment">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="343" parent="260" name="region">
      <Comment>区域</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="344" parent="260" name="rma_order">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="345" parent="260" name="rma_order_status_log">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="346" parent="260" name="seller_auth_product_lines_mapping">
      <Comment>授权卖家产品线映射</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="347" parent="260" name="seller_auth_region_mapping">
      <Comment>授权卖家授权区域映射</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="348" parent="260" name="seller_auth_violations">
      <Comment>授权卖家违规信息</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="349" parent="260" name="seller_authorization">
      <Comment>授权卖家管理</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="350" parent="260" name="tb_wms_vc_listing">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="351" parent="260" name="test_data">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="352" parent="260" name="test_data_child">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="353" parent="260" name="test_tree">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="354" parent="260" name="website_config">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="355" parent="260" name="website_setting">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="356" parent="260" name="work_order">
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <column id="357" parent="268" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="358" parent="268" name="name">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="359" parent="268" name="region_id">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="360" parent="268" name="phone">
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="361" parent="268" name="email">
      <Position>5</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="362" parent="268" name="cover">
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="363" parent="268" name="link">
      <Position>7</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="364" parent="268" name="address">
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="365" parent="268" name="create_at">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="366" parent="268" name="sort">
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="367" parent="268" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="368" parent="268" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="369" parent="269" name="id">
      <AutoIncrement>20</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="370" parent="269" name="seller_auth_id">
      <Comment>授权卖家id</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="371" parent="269" name="auth_platform">
      <Comment>授权平台</Comment>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="372" parent="269" name="store_name">
      <Comment>店铺名称</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="373" parent="269" name="store_link">
      <Comment>店铺链接</Comment>
      <Position>5</Position>
      <StoredType>varchar(600)|0s</StoredType>
    </column>
    <index id="374" parent="269" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="375" parent="269" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="376" parent="270" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="377" parent="270" name="image">
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="378" parent="270" name="name">
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="379" parent="270" name="link">
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="380" parent="270" name="title">
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="381" parent="270" name="desc">
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="382" parent="270" name="button_title">
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="383" parent="270" name="create_at">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="384" parent="270" name="sort">
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="385" parent="270" name="mobile_image">
      <Position>10</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="386" parent="270" name="mobile_link">
      <Position>11</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="387" parent="271" name="id">
      <AutoIncrement>123</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="388" parent="271" name="user_id">
      <Position>2</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="389" parent="271" name="sn">
      <Position>3</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="390" parent="271" name="sn_name">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="391" parent="271" name="product_id">
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="392" parent="271" name="shopify_product_id">
      <Position>6</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="393" parent="271" name="skuId">
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="394" parent="271" name="shopify_variant_id">
      <Position>8</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="395" parent="271" name="count">
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="396" parent="271" name="price">
      <Position>10</Position>
      <StoredType>decimal(8,2 digit)|0s</StoredType>
    </column>
    <column id="397" parent="271" name="create_at">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="398" parent="271" name="msrp_price">
      <Position>12</Position>
      <StoredType>decimal(8,2 digit)|0s</StoredType>
    </column>
    <index id="399" parent="271" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="400" parent="271" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="401" parent="272" name="code">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="402" parent="272" name="name">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="403" parent="272" name="banner_media">
      <Position>3</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="404" parent="272" name="menu_media">
      <Position>4</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="405" parent="272" name="pro_media">
      <Position>5</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="406" parent="272" name="nav_desc">
      <Position>6</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="407" parent="272" name="description">
      <Position>7</Position>
      <StoredType>varchar(1025)|0s</StoredType>
    </column>
    <column id="408" parent="272" name="parent_code">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="409" parent="272" name="parent_codes">
      <Position>9</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="410" parent="272" name="tree_sort">
      <Position>10</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="411" parent="272" name="tree_sorts">
      <Position>11</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="412" parent="272" name="tree_leaf">
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="413" parent="272" name="tree_level">
      <Position>13</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="414" parent="272" name="tree_names">
      <Position>14</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="415" parent="272" name="create_by">
      <Position>15</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="416" parent="272" name="create_date">
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="417" parent="272" name="update_by">
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="418" parent="272" name="update_date">
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="419" parent="272" name="remarks">
      <Position>19</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="420" parent="272" name="contrast_group_name">
      <Comment>对比分组名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="421" parent="272" name="PRIMARY">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="422" parent="272" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="423" parent="273" name="description">
      <Position>1</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="424" parent="273" name="media_url">
      <Position>2</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="425" parent="273" name="name_path">
      <Position>3</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="426" parent="273" name="id_path">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="427" parent="273" name="parent_id">
      <Position>5</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="428" parent="273" name="create_at">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="429" parent="273" name="name">
      <Position>7</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="430" parent="273" name="id">
      <Position>8</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="431" parent="274" name="id">
      <AutoIncrement>23</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="432" parent="274" name="classification_code">
      <Comment>分类编码</Comment>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="433" parent="274" name="draft">
      <Comment>是否草稿</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="434" parent="274" name="release_date">
      <Comment>发布时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="435" parent="274" name="create_by">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="436" parent="274" name="create_date">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="437" parent="274" name="update_by">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="438" parent="274" name="update_date">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="439" parent="274" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="440" parent="274" name="idx_classification">
      <ColNames>classification_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="441" parent="274" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="442" parent="275" name="id">
      <Comment>ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="443" parent="275" name="classification_code">
      <Comment>分类编码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="444" parent="275" name="icon_default">
      <Comment>图标(默认)</Comment>
      <Position>3</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="445" parent="275" name="icon_hover">
      <Comment>图标(鼠标移入)</Comment>
      <Position>4</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="446" parent="275" name="nav_text">
      <Comment>导航文案</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="447" parent="275" name="category_text">
      <Comment>二级品类文案</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="448" parent="275" name="sort_order">
      <Comment>排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="449" parent="275" name="is_display">
      <Comment>是否展示(0:否,1:是)</Comment>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="450" parent="275" name="jump_link">
      <Comment>跳转链接</Comment>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="451" parent="275" name="create_by">
      <Comment>创建人</Comment>
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="452" parent="275" name="create_date">
      <Comment>创建时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="453" parent="275" name="update_by">
      <Comment>更新人</Comment>
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="454" parent="275" name="update_date">
      <Comment>更新时间</Comment>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="455" parent="275" name="remarks">
      <Comment>备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="456" parent="275" name="del_flag">
      <Comment>删除标记(0:正常,1:删除)</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <index id="457" parent="275" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="458" parent="275" name="idx_classification_code">
      <ColNames>classification_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="459" parent="275" name="idx_sort_order">
      <ColNames>sort_order</ColNames>
      <Type>btree</Type>
    </index>
    <index id="460" parent="275" name="idx_is_display">
      <ColNames>is_display</ColNames>
      <Type>btree</Type>
    </index>
    <key id="461" parent="275" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="462" parent="276" name="id">
      <AutoIncrement>366</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="463" parent="276" name="type">
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="464" parent="276" name="email">
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="465" parent="276" name="phone_number">
      <Comment>电话号码</Comment>
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="466" parent="276" name="first_name">
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="467" parent="276" name="last_name">
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="468" parent="276" name="company_name">
      <Position>7</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="469" parent="276" name="company_size">
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="470" parent="276" name="website_url">
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="471" parent="276" name="country">
      <Position>10</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="472" parent="276" name="address">
      <Position>11</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="473" parent="276" name="brand">
      <Position>12</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="474" parent="276" name="message">
      <Position>13</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="475" parent="276" name="create_at">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="476" parent="276" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="477" parent="276" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="478" parent="277" name="id">
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="479" parent="277" name="year">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="480" parent="277" name="month">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="481" parent="277" name="day">
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="482" parent="277" name="operator">
      <Position>5</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="483" parent="277" name="tower_id">
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="484" parent="277" name="power">
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="485" parent="277" name="total_fee">
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="486" parent="277" name="environment_fee">
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="487" parent="277" name="electrical_loss_fee">
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="488" parent="278" name="id">
      <AutoIncrement>1921</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(40)|0s</StoredType>
    </column>
    <column id="489" parent="278" name="email">
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="490" parent="278" name="from">
      <Position>3</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="491" parent="278" name="create_at">
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="492" parent="278" name="site">
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="493" parent="278" name="copied_discount_code">
      <Comment>是否复制折扣码 0否 1是</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="494" parent="278" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="495" parent="278" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="496" parent="279" name="create_at">
      <Position>1</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="497" parent="279" name="create_by">
      <Position>2</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="498" parent="279" name="size">
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="499" parent="279" name="mime">
      <Position>4</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="500" parent="279" name="name">
      <Position>5</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="501" parent="279" name="id">
      <AutoIncrement>854</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <index id="502" parent="279" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="503" parent="279" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="504" parent="280" name="code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="505" parent="280" name="name">
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="506" parent="280" name="parent_code">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="507" parent="280" name="parent_codes">
      <Position>4</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="508" parent="280" name="tree_sort">
      <Position>5</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="509" parent="280" name="tree_sorts">
      <Position>6</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="510" parent="280" name="tree_leaf">
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="511" parent="280" name="tree_level">
      <Position>8</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="512" parent="280" name="tree_names">
      <Position>9</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="513" parent="280" name="link">
      <Position>10</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="514" parent="280" name="create_by">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="515" parent="280" name="create_date">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="516" parent="280" name="update_by">
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="517" parent="280" name="update_date">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="518" parent="280" name="remarks">
      <Position>15</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="519" parent="280" name="mobile_link">
      <Position>16</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="520" parent="281" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="521" parent="281" name="product_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="522" parent="281" name="name">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="523" parent="281" name="content">
      <Position>4</Position>
      <StoredType>mediumblob|0s</StoredType>
    </column>
    <column id="524" parent="281" name="media_url">
      <Position>5</Position>
      <StoredType>varchar(600)|0s</StoredType>
    </column>
    <column id="525" parent="281" name="create_at">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="526" parent="281" name="update_at">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="527" parent="281" name="download_url">
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="528" parent="281" name="type">
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="529" parent="281" name="group_by">
      <Position>10</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="530" parent="281" name="other_param">
      <Position>11</Position>
      <StoredType>varchar(5000)|0s</StoredType>
    </column>
    <column id="531" parent="281" name="file_type">
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="532" parent="281" name="sort">
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="533" parent="281" name="information_group_id">
      <Position>14</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="534" parent="281" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="535" parent="281" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="536" parent="282" name="id">
      <AutoIncrement>57</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="537" parent="282" name="parent_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="538" parent="282" name="product_id">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="539" parent="282" name="name">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="540" parent="282" name="type">
      <Position>5</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="541" parent="282" name="sort">
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="542" parent="282" name="create_by">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="543" parent="282" name="create_date">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="544" parent="282" name="update_by">
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="545" parent="282" name="update_date">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="546" parent="282" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="547" parent="282" name="idx_parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="548" parent="282" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="549" parent="283" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="550" parent="283" name="allowUrls">
      <Position>2</Position>
      <StoredType>varchar(2000)|0s</StoredType>
    </column>
    <column id="551" parent="283" name="clientId">
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="552" parent="283" name="clientSecret">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="553" parent="283" name="contact">
      <Position>5</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="554" parent="283" name="createTime">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="555" parent="283" name="deleteReason">
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="556" parent="283" name="deleteTime">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="557" parent="283" name="deleted">
      <Position>9</Position>
      <StoredType>bit(1)|0s</StoredType>
    </column>
    <column id="558" parent="283" name="memo">
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <index id="559" parent="283" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="560" parent="283" name="UK_se8r9y2ddrfi9iybmdctg6b38">
      <ColNames>clientId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="561" parent="283" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="562" parent="283" name="UK_se8r9y2ddrfi9iybmdctg6b38">
      <UnderlyingIndexName>UK_se8r9y2ddrfi9iybmdctg6b38</UnderlyingIndexName>
    </key>
    <column id="563" parent="284" name="table_name">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="564" parent="284" name="class_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="565" parent="284" name="comments">
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="566" parent="284" name="parent_table_name">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="567" parent="284" name="parent_table_fk_name">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="568" parent="284" name="data_source_name">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="569" parent="284" name="tpl_category">
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="570" parent="284" name="package_name">
      <Position>8</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="571" parent="284" name="module_name">
      <Position>9</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="572" parent="284" name="sub_module_name">
      <Position>10</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="573" parent="284" name="function_name">
      <Position>11</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="574" parent="284" name="function_name_simple">
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="575" parent="284" name="function_author">
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="576" parent="284" name="gen_base_dir">
      <Position>14</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="577" parent="284" name="options">
      <Position>15</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="578" parent="284" name="create_by">
      <Position>16</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="579" parent="284" name="create_date">
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="580" parent="284" name="update_by">
      <Position>18</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="581" parent="284" name="update_date">
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="582" parent="284" name="remarks">
      <Position>20</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="583" parent="285" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="584" parent="285" name="table_name">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="585" parent="285" name="column_name">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="586" parent="285" name="column_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="587" parent="285" name="column_type">
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="588" parent="285" name="column_label">
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="589" parent="285" name="comments">
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="590" parent="285" name="attr_name">
      <Position>8</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="591" parent="285" name="attr_type">
      <Position>9</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="592" parent="285" name="is_pk">
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="593" parent="285" name="is_null">
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="594" parent="285" name="is_insert">
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="595" parent="285" name="is_update">
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="596" parent="285" name="is_list">
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="597" parent="285" name="is_query">
      <Position>15</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="598" parent="285" name="query_type">
      <Position>16</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="599" parent="285" name="is_edit">
      <Position>17</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="600" parent="285" name="show_type">
      <Position>18</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="601" parent="285" name="options">
      <Position>19</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="602" parent="286" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="603" parent="286" name="TRIGGER_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="604" parent="286" name="TRIGGER_GROUP">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="605" parent="286" name="BLOB_DATA">
      <Position>4</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="606" parent="287" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="607" parent="287" name="CALENDAR_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="608" parent="287" name="CALENDAR">
      <Position>3</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="609" parent="288" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="610" parent="288" name="TRIGGER_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="611" parent="288" name="TRIGGER_GROUP">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="612" parent="288" name="CRON_EXPRESSION">
      <Position>4</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="613" parent="288" name="TIME_ZONE_ID">
      <Position>5</Position>
      <StoredType>varchar(80)|0s</StoredType>
    </column>
    <column id="614" parent="289" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="615" parent="289" name="ENTRY_ID">
      <Position>2</Position>
      <StoredType>varchar(95)|0s</StoredType>
    </column>
    <column id="616" parent="289" name="TRIGGER_NAME">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="617" parent="289" name="TRIGGER_GROUP">
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="618" parent="289" name="INSTANCE_NAME">
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="619" parent="289" name="FIRED_TIME">
      <Position>6</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="620" parent="289" name="SCHED_TIME">
      <Position>7</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="621" parent="289" name="PRIORITY">
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="622" parent="289" name="STATE">
      <Position>9</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="623" parent="289" name="JOB_NAME">
      <Position>10</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="624" parent="289" name="JOB_GROUP">
      <Position>11</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="625" parent="289" name="IS_NONCONCURRENT">
      <Position>12</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="626" parent="289" name="REQUESTS_RECOVERY">
      <Position>13</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="627" parent="290" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="628" parent="290" name="JOB_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="629" parent="290" name="JOB_GROUP">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="630" parent="290" name="DESCRIPTION">
      <Position>4</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="631" parent="290" name="JOB_CLASS_NAME">
      <Position>5</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="632" parent="290" name="IS_DURABLE">
      <Position>6</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="633" parent="290" name="IS_NONCONCURRENT">
      <Position>7</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="634" parent="290" name="IS_UPDATE_DATA">
      <Position>8</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="635" parent="290" name="REQUESTS_RECOVERY">
      <Position>9</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="636" parent="290" name="JOB_DATA">
      <Position>10</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="637" parent="291" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="638" parent="291" name="LOCK_NAME">
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="639" parent="292" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="640" parent="292" name="TRIGGER_GROUP">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="641" parent="293" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="642" parent="293" name="INSTANCE_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="643" parent="293" name="LAST_CHECKIN_TIME">
      <Position>3</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="644" parent="293" name="CHECKIN_INTERVAL">
      <Position>4</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="645" parent="294" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="646" parent="294" name="TRIGGER_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="647" parent="294" name="TRIGGER_GROUP">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="648" parent="294" name="REPEAT_COUNT">
      <Position>4</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="649" parent="294" name="REPEAT_INTERVAL">
      <Position>5</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="650" parent="294" name="TIMES_TRIGGERED">
      <Position>6</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="651" parent="295" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="652" parent="295" name="TRIGGER_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="653" parent="295" name="TRIGGER_GROUP">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="654" parent="295" name="STR_PROP_1">
      <Position>4</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="655" parent="295" name="STR_PROP_2">
      <Position>5</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="656" parent="295" name="STR_PROP_3">
      <Position>6</Position>
      <StoredType>varchar(512)|0s</StoredType>
    </column>
    <column id="657" parent="295" name="INT_PROP_1">
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="658" parent="295" name="INT_PROP_2">
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="659" parent="295" name="LONG_PROP_1">
      <Position>9</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="660" parent="295" name="LONG_PROP_2">
      <Position>10</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="661" parent="295" name="DEC_PROP_1">
      <Position>11</Position>
      <StoredType>decimal(13,4 digit)|0s</StoredType>
    </column>
    <column id="662" parent="295" name="DEC_PROP_2">
      <Position>12</Position>
      <StoredType>decimal(13,4 digit)|0s</StoredType>
    </column>
    <column id="663" parent="295" name="BOOL_PROP_1">
      <Position>13</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="664" parent="295" name="BOOL_PROP_2">
      <Position>14</Position>
      <StoredType>varchar(1)|0s</StoredType>
    </column>
    <column id="665" parent="296" name="SCHED_NAME">
      <Position>1</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="666" parent="296" name="TRIGGER_NAME">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="667" parent="296" name="TRIGGER_GROUP">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="668" parent="296" name="JOB_NAME">
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="669" parent="296" name="JOB_GROUP">
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="670" parent="296" name="DESCRIPTION">
      <Position>6</Position>
      <StoredType>varchar(250)|0s</StoredType>
    </column>
    <column id="671" parent="296" name="NEXT_FIRE_TIME">
      <Position>7</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="672" parent="296" name="PREV_FIRE_TIME">
      <Position>8</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="673" parent="296" name="PRIORITY">
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="674" parent="296" name="TRIGGER_STATE">
      <Position>10</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="675" parent="296" name="TRIGGER_TYPE">
      <Position>11</Position>
      <StoredType>varchar(8)|0s</StoredType>
    </column>
    <column id="676" parent="296" name="START_TIME">
      <Position>12</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="677" parent="296" name="END_TIME">
      <Position>13</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="678" parent="296" name="CALENDAR_NAME">
      <Position>14</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="679" parent="296" name="MISFIRE_INSTR">
      <Position>15</Position>
      <StoredType>smallint(6)|0s</StoredType>
    </column>
    <column id="680" parent="296" name="JOB_DATA">
      <Position>16</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="681" parent="297" name="id">
      <Position>1</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="682" parent="297" name="code">
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="683" parent="297" name="user_id">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="684" parent="297" name="secret">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="685" parent="297" name="app">
      <Position>5</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="686" parent="297" name="account">
      <Position>6</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="687" parent="297" name="client">
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="688" parent="297" name="ip">
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="689" parent="297" name="expire_time">
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="690" parent="297" name="create_at">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="691" parent="297" name="is_disabled">
      <Position>11</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <column id="692" parent="298" name="area_code">
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="693" parent="298" name="parent_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="694" parent="298" name="parent_codes">
      <Position>3</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="695" parent="298" name="tree_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="696" parent="298" name="tree_sorts">
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="697" parent="298" name="tree_leaf">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="698" parent="298" name="tree_level">
      <Position>7</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="699" parent="298" name="tree_names">
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="700" parent="298" name="area_name">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="701" parent="298" name="area_type">
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="702" parent="298" name="status">
      <Position>11</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="703" parent="298" name="create_by">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="704" parent="298" name="create_date">
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="705" parent="298" name="update_by">
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="706" parent="298" name="update_date">
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="707" parent="298" name="remarks">
      <Position>16</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="708" parent="299" name="company_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="709" parent="299" name="parent_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="710" parent="299" name="parent_codes">
      <Position>3</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="711" parent="299" name="tree_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="712" parent="299" name="tree_sorts">
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="713" parent="299" name="tree_leaf">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="714" parent="299" name="tree_level">
      <Position>7</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="715" parent="299" name="tree_names">
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="716" parent="299" name="view_code">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="717" parent="299" name="company_name">
      <Position>10</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="718" parent="299" name="full_name">
      <Position>11</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="719" parent="299" name="area_code">
      <Position>12</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="720" parent="299" name="status">
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="721" parent="299" name="create_by">
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="722" parent="299" name="create_date">
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="723" parent="299" name="update_by">
      <Position>16</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="724" parent="299" name="update_date">
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="725" parent="299" name="remarks">
      <Position>18</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="726" parent="299" name="corp_code">
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="727" parent="299" name="corp_name">
      <Position>20</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="728" parent="299" name="extend_s1">
      <Position>21</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="729" parent="299" name="extend_s2">
      <Position>22</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="730" parent="299" name="extend_s3">
      <Position>23</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="731" parent="299" name="extend_s4">
      <Position>24</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="732" parent="299" name="extend_s5">
      <Position>25</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="733" parent="299" name="extend_s6">
      <Position>26</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="734" parent="299" name="extend_s7">
      <Position>27</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="735" parent="299" name="extend_s8">
      <Position>28</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="736" parent="299" name="extend_i1">
      <Position>29</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="737" parent="299" name="extend_i2">
      <Position>30</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="738" parent="299" name="extend_i3">
      <Position>31</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="739" parent="299" name="extend_i4">
      <Position>32</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="740" parent="299" name="extend_f1">
      <Position>33</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="741" parent="299" name="extend_f2">
      <Position>34</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="742" parent="299" name="extend_f3">
      <Position>35</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="743" parent="299" name="extend_f4">
      <Position>36</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="744" parent="299" name="extend_d1">
      <Position>37</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="745" parent="299" name="extend_d2">
      <Position>38</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="746" parent="299" name="extend_d3">
      <Position>39</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="747" parent="299" name="extend_d4">
      <Position>40</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="748" parent="300" name="company_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="749" parent="300" name="office_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="750" parent="301" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="751" parent="301" name="config_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="752" parent="301" name="config_key">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="753" parent="301" name="config_value">
      <Position>4</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="754" parent="301" name="is_sys">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="755" parent="301" name="create_by">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="756" parent="301" name="create_date">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="757" parent="301" name="update_by">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="758" parent="301" name="update_date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="759" parent="301" name="remarks">
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="760" parent="302" name="dict_code">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="761" parent="302" name="parent_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="762" parent="302" name="parent_codes">
      <Position>3</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="763" parent="302" name="tree_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="764" parent="302" name="tree_sorts">
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="765" parent="302" name="tree_leaf">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="766" parent="302" name="tree_level">
      <Position>7</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="767" parent="302" name="tree_names">
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="768" parent="302" name="dict_label">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="769" parent="302" name="dict_value">
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="770" parent="302" name="dict_icon">
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="771" parent="302" name="dict_type">
      <Position>12</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="772" parent="302" name="is_sys">
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="773" parent="302" name="description">
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="774" parent="302" name="css_style">
      <Position>15</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="775" parent="302" name="css_class">
      <Position>16</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="776" parent="302" name="status">
      <Position>17</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="777" parent="302" name="create_by">
      <Position>18</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="778" parent="302" name="create_date">
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="779" parent="302" name="update_by">
      <Position>20</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="780" parent="302" name="update_date">
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="781" parent="302" name="remarks">
      <Position>22</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="782" parent="302" name="corp_code">
      <Position>23</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="783" parent="302" name="corp_name">
      <Position>24</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="784" parent="302" name="extend_s1">
      <Position>25</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="785" parent="302" name="extend_s2">
      <Position>26</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="786" parent="302" name="extend_s3">
      <Position>27</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="787" parent="302" name="extend_s4">
      <Position>28</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="788" parent="302" name="extend_s5">
      <Position>29</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="789" parent="302" name="extend_s6">
      <Position>30</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="790" parent="302" name="extend_s7">
      <Position>31</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="791" parent="302" name="extend_s8">
      <Position>32</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="792" parent="302" name="extend_i1">
      <Position>33</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="793" parent="302" name="extend_i2">
      <Position>34</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="794" parent="302" name="extend_i3">
      <Position>35</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="795" parent="302" name="extend_i4">
      <Position>36</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="796" parent="302" name="extend_f1">
      <Position>37</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="797" parent="302" name="extend_f2">
      <Position>38</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="798" parent="302" name="extend_f3">
      <Position>39</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="799" parent="302" name="extend_f4">
      <Position>40</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="800" parent="302" name="extend_d1">
      <Position>41</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="801" parent="302" name="extend_d2">
      <Position>42</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="802" parent="302" name="extend_d3">
      <Position>43</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="803" parent="302" name="extend_d4">
      <Position>44</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="804" parent="302" name="PRIMARY">
      <ColNames>dict_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="805" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="806" parent="303" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="807" parent="303" name="dict_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="808" parent="303" name="dict_type">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="809" parent="303" name="is_sys">
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="810" parent="303" name="status">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="811" parent="303" name="create_by">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="812" parent="303" name="create_date">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="813" parent="303" name="update_by">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="814" parent="303" name="update_date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="815" parent="303" name="remarks">
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="816" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="817" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="818" parent="304" name="emp_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="819" parent="304" name="emp_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="820" parent="304" name="emp_name_en">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="821" parent="304" name="emp_no">
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="822" parent="304" name="office_code">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="823" parent="304" name="office_name">
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="824" parent="304" name="company_code">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="825" parent="304" name="company_name">
      <Position>8</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="826" parent="304" name="status">
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="827" parent="304" name="create_by">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="828" parent="304" name="create_date">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="829" parent="304" name="update_by">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="830" parent="304" name="update_date">
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="831" parent="304" name="remarks">
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="832" parent="304" name="corp_code">
      <Position>15</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="833" parent="304" name="corp_name">
      <Position>16</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="834" parent="305" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="835" parent="305" name="emp_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="836" parent="305" name="office_code">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="837" parent="305" name="post_code">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="838" parent="306" name="emp_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="839" parent="306" name="post_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="840" parent="307" name="file_id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="841" parent="307" name="file_md5">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="842" parent="307" name="file_path">
      <Position>3</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="843" parent="307" name="file_content_type">
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="844" parent="307" name="file_extension">
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="845" parent="307" name="file_size">
      <Position>6</Position>
      <StoredType>decimal(31)|0s</StoredType>
    </column>
    <column id="846" parent="307" name="file_meta">
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="847" parent="307" name="file_preview">
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="848" parent="308" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="849" parent="308" name="file_id">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="850" parent="308" name="file_name">
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="851" parent="308" name="file_type">
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="852" parent="308" name="file_sort">
      <Position>5</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="853" parent="308" name="biz_key">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="854" parent="308" name="biz_type">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="855" parent="308" name="status">
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="856" parent="308" name="create_by">
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="857" parent="308" name="create_date">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="858" parent="308" name="update_by">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="859" parent="308" name="update_date">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="860" parent="308" name="remarks">
      <Position>13</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="861" parent="308" name="extend_s1">
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="862" parent="308" name="extend_s2">
      <Position>15</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="863" parent="308" name="extend_s3">
      <Position>16</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="864" parent="308" name="extend_s4">
      <Position>17</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="865" parent="308" name="extend_s5">
      <Position>18</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="866" parent="308" name="extend_s6">
      <Position>19</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="867" parent="308" name="extend_s7">
      <Position>20</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="868" parent="308" name="extend_s8">
      <Position>21</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="869" parent="308" name="extend_i1">
      <Position>22</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="870" parent="308" name="extend_i2">
      <Position>23</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="871" parent="308" name="extend_i3">
      <Position>24</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="872" parent="308" name="extend_i4">
      <Position>25</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="873" parent="308" name="extend_f1">
      <Position>26</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="874" parent="308" name="extend_f2">
      <Position>27</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="875" parent="308" name="extend_f3">
      <Position>28</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="876" parent="308" name="extend_f4">
      <Position>29</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="877" parent="308" name="extend_d1">
      <Position>30</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="878" parent="308" name="extend_d2">
      <Position>31</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="879" parent="308" name="extend_d3">
      <Position>32</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="880" parent="308" name="extend_d4">
      <Position>33</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="881" parent="309" name="job_name">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="882" parent="309" name="job_group">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="883" parent="309" name="description">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="884" parent="309" name="invoke_target">
      <Position>4</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="885" parent="309" name="cron_expression">
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="886" parent="309" name="misfire_instruction">
      <Position>6</Position>
      <StoredType>decimal(1)|0s</StoredType>
    </column>
    <column id="887" parent="309" name="concurrent">
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="888" parent="309" name="instance_name">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="889" parent="309" name="status">
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="890" parent="309" name="create_by">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="891" parent="309" name="create_date">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="892" parent="309" name="update_by">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="893" parent="309" name="update_date">
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="894" parent="309" name="remarks">
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="895" parent="310" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="896" parent="310" name="job_name">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="897" parent="310" name="job_group">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="898" parent="310" name="job_type">
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="899" parent="310" name="job_event">
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="900" parent="310" name="job_message">
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="901" parent="310" name="is_exception">
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="902" parent="310" name="exception_info">
      <Position>8</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="903" parent="310" name="create_date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="904" parent="311" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="905" parent="311" name="module_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="906" parent="311" name="lang_code">
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="907" parent="311" name="lang_text">
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="908" parent="311" name="lang_type">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="909" parent="311" name="create_by">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="910" parent="311" name="create_date">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="911" parent="311" name="update_by">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="912" parent="311" name="update_date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="913" parent="311" name="remarks">
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="914" parent="312" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="915" parent="312" name="log_type">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="916" parent="312" name="log_title">
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="917" parent="312" name="create_by">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="918" parent="312" name="create_by_name">
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="919" parent="312" name="create_date">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="920" parent="312" name="request_uri">
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="921" parent="312" name="request_method">
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="922" parent="312" name="request_params">
      <Position>9</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="923" parent="312" name="diff_modify_data">
      <Position>10</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="924" parent="312" name="biz_key">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="925" parent="312" name="biz_type">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="926" parent="312" name="remote_addr">
      <Position>13</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="927" parent="312" name="server_addr">
      <Position>14</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="928" parent="312" name="is_exception">
      <Position>15</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="929" parent="312" name="exception_info">
      <Position>16</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="930" parent="312" name="user_agent">
      <Position>17</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="931" parent="312" name="device_name">
      <Position>18</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="932" parent="312" name="browser_name">
      <Position>19</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="933" parent="312" name="execute_time">
      <Position>20</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="934" parent="312" name="corp_code">
      <Position>21</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="935" parent="312" name="corp_name">
      <Position>22</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="936" parent="313" name="menu_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="937" parent="313" name="parent_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="938" parent="313" name="parent_codes">
      <Position>3</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="939" parent="313" name="tree_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="940" parent="313" name="tree_sorts">
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="941" parent="313" name="tree_leaf">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="942" parent="313" name="tree_level">
      <Position>7</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="943" parent="313" name="tree_names">
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="944" parent="313" name="menu_name">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="945" parent="313" name="menu_type">
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="946" parent="313" name="menu_href">
      <Position>11</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="947" parent="313" name="menu_target">
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="948" parent="313" name="menu_icon">
      <Position>13</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="949" parent="313" name="menu_color">
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="950" parent="313" name="menu_title">
      <Position>15</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="951" parent="313" name="permission">
      <Position>16</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="952" parent="313" name="weight">
      <Position>17</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="953" parent="313" name="is_show">
      <Position>18</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="954" parent="313" name="sys_code">
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="955" parent="313" name="module_codes">
      <Position>20</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="956" parent="313" name="status">
      <Position>21</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="957" parent="313" name="create_by">
      <Position>22</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="958" parent="313" name="create_date">
      <Position>23</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="959" parent="313" name="update_by">
      <Position>24</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="960" parent="313" name="update_date">
      <Position>25</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="961" parent="313" name="remarks">
      <Position>26</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="962" parent="313" name="extend_s1">
      <Position>27</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="963" parent="313" name="extend_s2">
      <Position>28</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="964" parent="313" name="extend_s3">
      <Position>29</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="965" parent="313" name="extend_s4">
      <Position>30</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="966" parent="313" name="extend_s5">
      <Position>31</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="967" parent="313" name="extend_s6">
      <Position>32</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="968" parent="313" name="extend_s7">
      <Position>33</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="969" parent="313" name="extend_s8">
      <Position>34</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="970" parent="313" name="extend_i1">
      <Position>35</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="971" parent="313" name="extend_i2">
      <Position>36</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="972" parent="313" name="extend_i3">
      <Position>37</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="973" parent="313" name="extend_i4">
      <Position>38</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="974" parent="313" name="extend_f1">
      <Position>39</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="975" parent="313" name="extend_f2">
      <Position>40</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="976" parent="313" name="extend_f3">
      <Position>41</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="977" parent="313" name="extend_f4">
      <Position>42</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="978" parent="313" name="extend_d1">
      <Position>43</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="979" parent="313" name="extend_d2">
      <Position>44</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="980" parent="313" name="extend_d3">
      <Position>45</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="981" parent="313" name="extend_d4">
      <Position>46</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="982" parent="314" name="module_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="983" parent="314" name="module_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="984" parent="314" name="description">
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="985" parent="314" name="main_class_name">
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="986" parent="314" name="current_version">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="987" parent="314" name="upgrade_info">
      <Position>6</Position>
      <StoredType>varchar(300)|0s</StoredType>
    </column>
    <column id="988" parent="314" name="status">
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="989" parent="314" name="create_by">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="990" parent="314" name="create_date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="991" parent="314" name="update_by">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="992" parent="314" name="update_date">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="993" parent="314" name="remarks">
      <Position>12</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="994" parent="315" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="995" parent="315" name="msg_title">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="996" parent="315" name="content_level">
      <Position>3</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="997" parent="315" name="content_type">
      <Position>4</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="998" parent="315" name="msg_content">
      <Position>5</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="999" parent="315" name="receive_type">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1000" parent="315" name="receive_codes">
      <Position>7</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1001" parent="315" name="receive_names">
      <Position>8</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1002" parent="315" name="send_user_code">
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1003" parent="315" name="send_user_name">
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1004" parent="315" name="send_date">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1005" parent="315" name="is_attac">
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1006" parent="315" name="notify_types">
      <Position>13</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1007" parent="315" name="status">
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1008" parent="315" name="create_by">
      <Position>15</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1009" parent="315" name="create_date">
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1010" parent="315" name="update_by">
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1011" parent="315" name="update_date">
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1012" parent="315" name="remarks">
      <Position>19</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1013" parent="316" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1014" parent="316" name="msg_inner_id">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1015" parent="316" name="receive_user_code">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1016" parent="316" name="receive_user_name">
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1017" parent="316" name="read_status">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1018" parent="316" name="read_date">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1019" parent="316" name="is_star">
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1020" parent="317" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1021" parent="317" name="msg_type">
      <Position>2</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1022" parent="317" name="msg_title">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1023" parent="317" name="msg_content">
      <Position>4</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1024" parent="317" name="biz_key">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1025" parent="317" name="biz_type">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1026" parent="317" name="receive_code">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1027" parent="317" name="receive_user_code">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1028" parent="317" name="receive_user_name">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1029" parent="317" name="send_user_code">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1030" parent="317" name="send_user_name">
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1031" parent="317" name="send_date">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1032" parent="317" name="is_merge_push">
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1033" parent="317" name="plan_push_date">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1034" parent="317" name="push_number">
      <Position>15</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1035" parent="317" name="push_return_code">
      <Position>16</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1036" parent="317" name="push_return_msg_id">
      <Position>17</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1037" parent="317" name="push_return_content">
      <Position>18</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1038" parent="317" name="push_status">
      <Position>19</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1039" parent="317" name="push_date">
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1040" parent="317" name="read_status">
      <Position>21</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1041" parent="317" name="read_date">
      <Position>22</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1042" parent="318" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1043" parent="318" name="msg_type">
      <Position>2</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1044" parent="318" name="msg_title">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1045" parent="318" name="msg_content">
      <Position>4</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1046" parent="318" name="biz_key">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1047" parent="318" name="biz_type">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1048" parent="318" name="receive_code">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1049" parent="318" name="receive_user_code">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1050" parent="318" name="receive_user_name">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1051" parent="318" name="send_user_code">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1052" parent="318" name="send_user_name">
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1053" parent="318" name="send_date">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1054" parent="318" name="is_merge_push">
      <Position>13</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1055" parent="318" name="plan_push_date">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1056" parent="318" name="push_number">
      <Position>15</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1057" parent="318" name="push_return_content">
      <Position>16</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1058" parent="318" name="push_return_code">
      <Position>17</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1059" parent="318" name="push_return_msg_id">
      <Position>18</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1060" parent="318" name="push_status">
      <Position>19</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1061" parent="318" name="push_date">
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1062" parent="318" name="read_status">
      <Position>21</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1063" parent="318" name="read_date">
      <Position>22</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1064" parent="319" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1065" parent="319" name="module_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1066" parent="319" name="tpl_key">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1067" parent="319" name="tpl_name">
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1068" parent="319" name="tpl_type">
      <Position>5</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1069" parent="319" name="tpl_content">
      <Position>6</Position>
      <StoredType>mediumtext|0s</StoredType>
    </column>
    <column id="1070" parent="319" name="status">
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1071" parent="319" name="create_by">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1072" parent="319" name="create_date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1073" parent="319" name="update_by">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1074" parent="319" name="update_date">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1075" parent="319" name="remarks">
      <Position>12</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1076" parent="320" name="office_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1077" parent="320" name="parent_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1078" parent="320" name="parent_codes">
      <Position>3</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1079" parent="320" name="tree_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1080" parent="320" name="tree_sorts">
      <Position>5</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1081" parent="320" name="tree_leaf">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1082" parent="320" name="tree_level">
      <Position>7</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="1083" parent="320" name="tree_names">
      <Position>8</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1084" parent="320" name="view_code">
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1085" parent="320" name="office_name">
      <Position>10</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1086" parent="320" name="full_name">
      <Position>11</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1087" parent="320" name="office_type">
      <Position>12</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1088" parent="320" name="leader">
      <Position>13</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1089" parent="320" name="phone">
      <Position>14</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1090" parent="320" name="address">
      <Position>15</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1091" parent="320" name="zip_code">
      <Position>16</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1092" parent="320" name="email">
      <Position>17</Position>
      <StoredType>varchar(300)|0s</StoredType>
    </column>
    <column id="1093" parent="320" name="status">
      <Position>18</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1094" parent="320" name="create_by">
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1095" parent="320" name="create_date">
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1096" parent="320" name="update_by">
      <Position>21</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1097" parent="320" name="update_date">
      <Position>22</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1098" parent="320" name="remarks">
      <Position>23</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1099" parent="320" name="corp_code">
      <Position>24</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1100" parent="320" name="corp_name">
      <Position>25</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1101" parent="320" name="extend_s1">
      <Position>26</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1102" parent="320" name="extend_s2">
      <Position>27</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1103" parent="320" name="extend_s3">
      <Position>28</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1104" parent="320" name="extend_s4">
      <Position>29</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1105" parent="320" name="extend_s5">
      <Position>30</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1106" parent="320" name="extend_s6">
      <Position>31</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1107" parent="320" name="extend_s7">
      <Position>32</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1108" parent="320" name="extend_s8">
      <Position>33</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1109" parent="320" name="extend_i1">
      <Position>34</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1110" parent="320" name="extend_i2">
      <Position>35</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1111" parent="320" name="extend_i3">
      <Position>36</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1112" parent="320" name="extend_i4">
      <Position>37</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1113" parent="320" name="extend_f1">
      <Position>38</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1114" parent="320" name="extend_f2">
      <Position>39</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1115" parent="320" name="extend_f3">
      <Position>40</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1116" parent="320" name="extend_f4">
      <Position>41</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1117" parent="320" name="extend_d1">
      <Position>42</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1118" parent="320" name="extend_d2">
      <Position>43</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1119" parent="320" name="extend_d3">
      <Position>44</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1120" parent="320" name="extend_d4">
      <Position>45</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1121" parent="321" name="post_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1122" parent="321" name="post_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1123" parent="321" name="post_type">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1124" parent="321" name="post_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1125" parent="321" name="status">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1126" parent="321" name="create_by">
      <Position>6</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1127" parent="321" name="create_date">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1128" parent="321" name="update_by">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1129" parent="321" name="update_date">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1130" parent="321" name="remarks">
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1131" parent="321" name="corp_code">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1132" parent="321" name="corp_name">
      <Position>12</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1133" parent="322" name="role_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1134" parent="322" name="role_name">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1135" parent="322" name="role_type">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1136" parent="322" name="role_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1137" parent="322" name="is_sys">
      <Position>5</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1138" parent="322" name="user_type">
      <Position>6</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1139" parent="322" name="data_scope">
      <Position>7</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1140" parent="322" name="biz_scope">
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1141" parent="322" name="status">
      <Position>9</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1142" parent="322" name="create_by">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1143" parent="322" name="create_date">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1144" parent="322" name="update_by">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1145" parent="322" name="update_date">
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1146" parent="322" name="remarks">
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1147" parent="322" name="corp_code">
      <Position>15</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1148" parent="322" name="corp_name">
      <Position>16</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1149" parent="322" name="extend_s1">
      <Position>17</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1150" parent="322" name="extend_s2">
      <Position>18</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1151" parent="322" name="extend_s3">
      <Position>19</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1152" parent="322" name="extend_s4">
      <Position>20</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1153" parent="322" name="extend_s5">
      <Position>21</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1154" parent="322" name="extend_s6">
      <Position>22</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1155" parent="322" name="extend_s7">
      <Position>23</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1156" parent="322" name="extend_s8">
      <Position>24</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1157" parent="322" name="extend_i1">
      <Position>25</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1158" parent="322" name="extend_i2">
      <Position>26</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1159" parent="322" name="extend_i3">
      <Position>27</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1160" parent="322" name="extend_i4">
      <Position>28</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1161" parent="322" name="extend_f1">
      <Position>29</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1162" parent="322" name="extend_f2">
      <Position>30</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1163" parent="322" name="extend_f3">
      <Position>31</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1164" parent="322" name="extend_f4">
      <Position>32</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1165" parent="322" name="extend_d1">
      <Position>33</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1166" parent="322" name="extend_d2">
      <Position>34</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1167" parent="322" name="extend_d3">
      <Position>35</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1168" parent="322" name="extend_d4">
      <Position>36</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1169" parent="323" name="role_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1170" parent="323" name="ctrl_type">
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1171" parent="323" name="ctrl_data">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1172" parent="323" name="ctrl_permi">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1173" parent="324" name="role_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1174" parent="324" name="menu_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1175" parent="325" name="user_code">
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1176" parent="325" name="login_code">
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1177" parent="325" name="user_name">
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1178" parent="325" name="password">
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1179" parent="325" name="email">
      <Position>5</Position>
      <StoredType>varchar(300)|0s</StoredType>
    </column>
    <column id="1180" parent="325" name="mobile">
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1181" parent="325" name="phone">
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1182" parent="325" name="sex">
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1183" parent="325" name="avatar">
      <Position>9</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1184" parent="325" name="sign">
      <Position>10</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1185" parent="325" name="wx_openid">
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1186" parent="325" name="mobile_imei">
      <Position>12</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1187" parent="325" name="user_type">
      <Position>13</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1188" parent="325" name="ref_code">
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1189" parent="325" name="ref_name">
      <Position>15</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1190" parent="325" name="mgr_type">
      <Position>16</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1191" parent="325" name="pwd_security_level">
      <Position>17</Position>
      <StoredType>decimal(1)|0s</StoredType>
    </column>
    <column id="1192" parent="325" name="pwd_update_date">
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1193" parent="325" name="pwd_update_record">
      <Position>19</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1194" parent="325" name="pwd_question">
      <Position>20</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1195" parent="325" name="pwd_question_answer">
      <Position>21</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1196" parent="325" name="pwd_question_2">
      <Position>22</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1197" parent="325" name="pwd_question_answer_2">
      <Position>23</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1198" parent="325" name="pwd_question_3">
      <Position>24</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1199" parent="325" name="pwd_question_answer_3">
      <Position>25</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1200" parent="325" name="pwd_quest_update_date">
      <Position>26</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1201" parent="325" name="last_login_ip">
      <Position>27</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1202" parent="325" name="last_login_date">
      <Position>28</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1203" parent="325" name="freeze_date">
      <Position>29</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1204" parent="325" name="freeze_cause">
      <Position>30</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1205" parent="325" name="user_weight">
      <Position>31</Position>
      <StoredType>decimal(8)|0s</StoredType>
    </column>
    <column id="1206" parent="325" name="status">
      <Position>32</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1207" parent="325" name="create_by">
      <Position>33</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1208" parent="325" name="create_date">
      <Position>34</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1209" parent="325" name="update_by">
      <Position>35</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1210" parent="325" name="update_date">
      <Position>36</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1211" parent="325" name="remarks">
      <Position>37</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1212" parent="325" name="corp_code">
      <Position>38</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1213" parent="325" name="corp_name">
      <Position>39</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1214" parent="325" name="extend_s1">
      <Position>40</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1215" parent="325" name="extend_s2">
      <Position>41</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1216" parent="325" name="extend_s3">
      <Position>42</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1217" parent="325" name="extend_s4">
      <Position>43</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1218" parent="325" name="extend_s5">
      <Position>44</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1219" parent="325" name="extend_s6">
      <Position>45</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1220" parent="325" name="extend_s7">
      <Position>46</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1221" parent="325" name="extend_s8">
      <Position>47</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1222" parent="325" name="extend_i1">
      <Position>48</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1223" parent="325" name="extend_i2">
      <Position>49</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1224" parent="325" name="extend_i3">
      <Position>50</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1225" parent="325" name="extend_i4">
      <Position>51</Position>
      <StoredType>decimal(19)|0s</StoredType>
    </column>
    <column id="1226" parent="325" name="extend_f1">
      <Position>52</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1227" parent="325" name="extend_f2">
      <Position>53</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1228" parent="325" name="extend_f3">
      <Position>54</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1229" parent="325" name="extend_f4">
      <Position>55</Position>
      <StoredType>decimal(19,4 digit)|0s</StoredType>
    </column>
    <column id="1230" parent="325" name="extend_d1">
      <Position>56</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1231" parent="325" name="extend_d2">
      <Position>57</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1232" parent="325" name="extend_d3">
      <Position>58</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1233" parent="325" name="extend_d4">
      <Position>59</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1234" parent="326" name="user_code">
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1235" parent="326" name="ctrl_type">
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1236" parent="326" name="ctrl_data">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1237" parent="326" name="ctrl_permi">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1238" parent="327" name="user_code">
      <Position>1</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1239" parent="327" name="role_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1240" parent="328" name="id">
      <AutoIncrement>2</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint(20)|0s</StoredType>
    </column>
    <column id="1241" parent="328" name="ip_address">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(45)|0s</StoredType>
    </column>
    <column id="1242" parent="328" name="ip_location">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1243" parent="328" name="menu_name">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1244" parent="328" name="product_name">
      <Comment>产品名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1245" parent="328" name="click_time">
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1246" parent="328" name="user_agent">
      <Position>7</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <index id="1247" parent="328" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1248" parent="328" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1249" parent="329" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1250" parent="329" name="name">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(120)|0s</StoredType>
    </column>
    <column id="1251" parent="329" name="content">
      <Position>3</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1252" parent="329" name="cover">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1253" parent="329" name="introduction">
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1254" parent="329" name="news_status">
      <DefaultExpression>&apos;10&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>varchar(4)|0s</StoredType>
    </column>
    <column id="1255" parent="329" name="create_at">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1256" parent="329" name="create_by">
      <Position>8</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1257" parent="329" name="update_at">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1258" parent="329" name="update_by">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1259" parent="329" name="category">
      <Position>11</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1260" parent="329" name="is_main">
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1261" parent="329" name="publish_at">
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1262" parent="329" name="is_top">
      <Position>14</Position>
      <StoredType>tinyint(4)|0s</StoredType>
    </column>
    <index id="1263" parent="329" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1264" parent="329" name="index_name">
      <ColNames>name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1265" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1266" parent="330" name="code">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1267" parent="330" name="name">
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1268" parent="330" name="url">
      <Comment>跳转链接</Comment>
      <Position>3</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1269" parent="330" name="parent_code">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1270" parent="330" name="parent_codes">
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1271" parent="330" name="tree_sort">
      <Position>6</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1272" parent="330" name="tree_sorts">
      <Position>7</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1273" parent="330" name="tree_leaf">
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1274" parent="330" name="tree_level">
      <Position>9</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="1275" parent="330" name="tree_names">
      <Position>10</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1276" parent="330" name="create_by">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1277" parent="330" name="create_date">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1278" parent="330" name="update_by">
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1279" parent="330" name="update_date">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1280" parent="330" name="PRIMARY">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1281" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1282" parent="331" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1283" parent="331" name="name">
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1284" parent="331" name="new_product">
      <Comment>是否新品</Comment>
      <DefaultExpression>b&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bit(1)|0s</StoredType>
    </column>
    <column id="1285" parent="331" name="classification_id">
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1286" parent="331" name="description">
      <Position>5</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1287" parent="331" name="search_cover">
      <Position>6</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1288" parent="331" name="cover">
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1289" parent="331" name="search_view">
      <DefaultExpression>b&apos;1&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>bit(1)|0s</StoredType>
    </column>
    <column id="1290" parent="331" name="sort">
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1291" parent="331" name="create_at">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1292" parent="331" name="mobile_cover">
      <Position>11</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1293" parent="331" name="discontinued">
      <Comment>是否停产</Comment>
      <DefaultExpression>b&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>bit(1)|0s</StoredType>
    </column>
    <column id="1294" parent="331" name="rma_tool_show">
      <Comment>是否RMA工具</Comment>
      <Position>13</Position>
      <StoredType>bit(1)|0s</StoredType>
    </column>
    <column id="1295" parent="331" name="allow_purchase">
      <Comment>是否允许购买</Comment>
      <DefaultExpression>b&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>bit(1)|0s</StoredType>
    </column>
    <column id="1296" parent="331" name="url_us">
      <Comment>US官网跳转链接</Comment>
      <Position>15</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1297" parent="331" name="url_eu">
      <Comment>EU官网跳转链接</Comment>
      <Position>16</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1298" parent="331" name="url_au">
      <Comment>AU官网跳转链接</Comment>
      <Position>17</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <index id="1299" parent="331" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1300" parent="331" name="idx_name">
      <ColNames>name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1301" parent="331" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1302" parent="332" name="id">
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1303" parent="332" name="product_id">
      <Comment>产品id</Comment>
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1304" parent="332" name="classification_id">
      <Comment>分类Id</Comment>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1305" parent="332" name="create_by">
      <Comment>创建人</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1306" parent="332" name="create_date">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1307" parent="332" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1308" parent="332" name="idx_pi_ci">
      <ColNames>product_id
classification_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1309" parent="332" name="idx_ci">
      <ColNames>classification_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1310" parent="332" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1311" parent="333" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1312" parent="333" name="product_id">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1313" parent="333" name="name">
      <Position>3</Position>
      <StoredType>varchar(56)|0s</StoredType>
    </column>
    <column id="1314" parent="333" name="image">
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1315" parent="333" name="type">
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1316" parent="333" name="param">
      <Position>6</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <index id="1317" parent="333" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1318" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1319" parent="334" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1320" parent="334" name="content">
      <Position>2</Position>
      <StoredType>blob|0s</StoredType>
    </column>
    <column id="1321" parent="334" name="name">
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1322" parent="334" name="product_id">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <index id="1323" parent="334" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1324" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1325" parent="335" name="id">
      <AutoIncrement>123</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1326" parent="335" name="parent_id">
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1327" parent="335" name="type">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1328" parent="335" name="name">
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1329" parent="335" name="create_by">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1330" parent="335" name="create_date">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1331" parent="335" name="update_by">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1332" parent="335" name="update_date">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1333" parent="335" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1334" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1335" parent="336" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1336" parent="336" name="product_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1337" parent="336" name="type">
      <Position>3</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1338" parent="336" name="desc">
      <Position>4</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1339" parent="336" name="image">
      <Position>5</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1340" parent="336" name="title">
      <Position>6</Position>
      <StoredType>varchar(56)|0s</StoredType>
    </column>
    <column id="1341" parent="336" name="background">
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1342" parent="336" name="title_color">
      <Position>8</Position>
      <StoredType>varchar(25)|0s</StoredType>
    </column>
    <column id="1343" parent="336" name="desc_color">
      <Position>9</Position>
      <StoredType>varchar(25)|0s</StoredType>
    </column>
    <index id="1344" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1345" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1346" parent="337" name="id">
      <AutoIncrement>68272</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1347" parent="337" name="product_id">
      <Comment>产品id</Comment>
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1348" parent="337" name="classification_compare_id">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1349" parent="337" name="category_id">
      <Comment>种类id</Comment>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1350" parent="337" name="val">
      <Comment>值</Comment>
      <Position>5</Position>
      <StoredType>varchar(400)|0s</StoredType>
    </column>
    <column id="1351" parent="337" name="sort">
      <Comment>排序</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1352" parent="337" name="draft">
      <Comment>草稿</Comment>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1353" parent="337" name="create_time">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1354" parent="337" name="create_by">
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1355" parent="337" name="update_time">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1356" parent="337" name="update_by">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1357" parent="337" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1358" parent="337" name="idx_product_id">
      <ColNames>product_id
draft</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1359" parent="337" name="idx_search">
      <ColNames>classification_compare_id
product_id
category_id
sort</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1360" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1361" parent="338" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1362" parent="338" name="product_id">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1363" parent="338" name="sort">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1364" parent="338" name="create_date">
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1365" parent="338" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1366" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1367" parent="339" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1368" parent="339" name="product_id">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1369" parent="339" name="relation_product_id">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1370" parent="339" name="create_date">
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1371" parent="339" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1372" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1373" parent="340" name="product_id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1374" parent="340" name="infomation_id">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="1375" parent="340" name="PRIMARY">
      <ColNames>product_id
infomation_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1376" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1377" parent="341" name="id">
      <AutoIncrement>5456</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1378" parent="341" name="classification_compare_id">
      <Comment>产品分类比较id</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1379" parent="341" name="parent_id">
      <Comment>父id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1380" parent="341" name="category">
      <Comment>属性种类</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1381" parent="341" name="sort">
      <Comment>排序</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1382" parent="341" name="draft">
      <Comment>是否草稿</Comment>
      <Position>6</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="1383" parent="341" name="create_by">
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1384" parent="341" name="create_date">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1385" parent="341" name="update_by">
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1386" parent="341" name="update_date">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1387" parent="341" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1388" parent="341" name="idx_search">
      <ColNames>classification_compare_id
parent_id
sort</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1389" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1390" parent="342" name="id">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1391" parent="342" name="name">
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1392" parent="342" name="people">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1393" parent="342" name="position">
      <Position>4</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1394" parent="342" name="create_at">
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1395" parent="342" name="content">
      <Position>6</Position>
      <StoredType>varchar(10240)|0s</StoredType>
    </column>
    <column id="1396" parent="342" name="category">
      <Position>7</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1397" parent="342" name="type">
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <index id="1398" parent="342" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1399" parent="342" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1400" parent="343" name="code">
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1401" parent="343" name="name">
      <Comment>名字</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1402" parent="343" name="flag">
      <Comment>国旗</Comment>
      <Position>3</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1403" parent="343" name="parent_code">
      <Comment>父级编号
</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1404" parent="343" name="parent_codes">
      <Comment>所有父级编号</Comment>
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1405" parent="343" name="tree_sort">
      <Comment>本级排序号（升序）</Comment>
      <Position>6</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1406" parent="343" name="tree_sorts">
      <Comment>所有级别排序号</Comment>
      <Position>7</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1407" parent="343" name="tree_leaf">
      <Comment>是否最末级</Comment>
      <Position>8</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1408" parent="343" name="tree_level">
      <Comment>层次级别</Comment>
      <Position>9</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="1409" parent="343" name="tree_names">
      <Comment>全节点名</Comment>
      <Position>10</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1410" parent="343" name="create_by">
      <Comment>创建人</Comment>
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1411" parent="343" name="create_date">
      <Comment>创建时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1412" parent="343" name="update_by">
      <Comment>更新人</Comment>
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1413" parent="343" name="update_date">
      <Comment>更新时间</Comment>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1414" parent="343" name="remarks">
      <Comment>备注</Comment>
      <Position>15</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1415" parent="343" name="old_code">
      <Position>16</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <index id="1416" parent="343" name="PRIMARY">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1417" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1418" parent="344" name="id">
      <AutoIncrement>1668</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1419" parent="344" name="ticket_number">
      <Position>2</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="1420" parent="344" name="ticket_id">
      <Position>3</Position>
      <StoredType>varchar(30)|0s</StoredType>
    </column>
    <column id="1421" parent="344" name="user_id">
      <Position>4</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="1422" parent="344" name="sn">
      <Position>5</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="1423" parent="344" name="product_id">
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1424" parent="344" name="issue_type">
      <Position>7</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="1425" parent="344" name="classification_id">
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1426" parent="344" name="product_name">
      <Position>9</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1427" parent="344" name="description">
      <Position>10</Position>
      <StoredType>varchar(3000)|0s</StoredType>
    </column>
    <column id="1428" parent="344" name="order_no">
      <Position>11</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1429" parent="344" name="country">
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1430" parent="344" name="state_region">
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1431" parent="344" name="city">
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1432" parent="344" name="address1">
      <Position>15</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1433" parent="344" name="postal_code">
      <Position>16</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1434" parent="344" name="seller_name">
      <Position>17</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1435" parent="344" name="platform">
      <Position>18</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1436" parent="344" name="channel">
      <DefaultExpression>&apos;Offcial website form&apos;</DefaultExpression>
      <Position>19</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="1437" parent="344" name="ticket_status">
      <Position>20</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1438" parent="344" name="status_modified_time">
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1439" parent="344" name="tui_hui_gen_zong_hao">
      <Position>22</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1440" parent="344" name="huo_wu_liu_dan_hao">
      <Position>23</Position>
      <StoredType>varchar(128)|0s</StoredType>
    </column>
    <column id="1441" parent="344" name="chu_li_fang_an">
      <Position>24</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1442" parent="344" name="jing_xiao_shang_ming_cheng">
      <Position>25</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1443" parent="344" name="email">
      <Position>26</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1444" parent="344" name="phone">
      <Position>27</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1445" parent="344" name="solution">
      <Position>28</Position>
      <StoredType>varchar(3000)|0s</StoredType>
    </column>
    <column id="1446" parent="344" name="send_zoho">
      <DefaultExpression>b&apos;0&apos;</DefaultExpression>
      <Position>29</Position>
      <StoredType>bit(1)|0s</StoredType>
    </column>
    <column id="1447" parent="344" name="create_time">
      <Position>30</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1448" parent="344" name="update_time">
      <Position>31</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1449" parent="344" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1450" parent="344" name="idx_ticketNumber">
      <ColNames>ticket_number</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1451" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1452" parent="345" name="id">
      <AutoIncrement>2983</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1453" parent="345" name="rma_order_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1454" parent="345" name="ticket_number">
      <Position>3</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="1455" parent="345" name="status">
      <Position>4</Position>
      <StoredType>varchar(36)|0s</StoredType>
    </column>
    <column id="1456" parent="345" name="status_modified_time">
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1457" parent="345" name="create_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1458" parent="345" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1459" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1460" parent="346" name="id">
      <AutoIncrement>78</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1461" parent="346" name="seller_auth_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1462" parent="346" name="product_lines_id">
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="1463" parent="346" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1464" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1465" parent="347" name="id">
      <AutoIncrement>23</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1466" parent="347" name="seller_auth_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1467" parent="347" name="region_code">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <index id="1468" parent="347" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1469" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1470" parent="348" name="id">
      <AutoIncrement>5</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1471" parent="348" name="seller_auth_id">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1472" parent="348" name="message">
      <Comment>违规信息</Comment>
      <Position>3</Position>
      <StoredType>varchar(600)|0s</StoredType>
    </column>
    <column id="1473" parent="348" name="date">
      <Position>4</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <index id="1474" parent="348" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1475" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1476" parent="349" name="id">
      <AutoIncrement>67</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1477" parent="349" name="auth_cert_no">
      <Comment>授权证书编号</Comment>
      <Position>2</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1478" parent="349" name="authorized_entity">
      <Comment>被授权主体</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1479" parent="349" name="status">
      <Comment>发布状态 0草稿 1正式</Comment>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1480" parent="349" name="auth_type">
      <Comment>授权类型 0普通 1独家</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1481" parent="349" name="auth_start_date">
      <Comment>授权起始日期</Comment>
      <Position>6</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1482" parent="349" name="auth_end_date">
      <Comment>授权结束日期</Comment>
      <Position>7</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="1483" parent="349" name="contact_phone">
      <Comment>联系电话</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1484" parent="349" name="contact_email">
      <Comment>联系邮箱</Comment>
      <Position>9</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1485" parent="349" name="contact_address">
      <Comment>联系地址</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="1486" parent="349" name="sales_rep">
      <Comment>业务员</Comment>
      <Position>11</Position>
      <StoredType>varchar(16)|0s</StoredType>
    </column>
    <column id="1487" parent="349" name="dealer_level">
      <Comment>经销商层级</Comment>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1488" parent="349" name="dealer_code">
      <Comment>经销商编码</Comment>
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1489" parent="349" name="dealer_name">
      <Comment>经销商名称</Comment>
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1490" parent="349" name="parent_dealer">
      <Comment>所属上级经销商</Comment>
      <Position>15</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1491" parent="349" name="other_msg">
      <Comment>其他</Comment>
      <Position>16</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1492" parent="349" name="create_by">
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1493" parent="349" name="create_date">
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1494" parent="349" name="update_by">
      <Position>19</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1495" parent="349" name="update_date">
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1496" parent="349" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1497" parent="349" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1498" parent="350" name="id">
      <AutoIncrement>1</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1499" parent="350" name="vc_store_id">
      <Comment>VC店铺id</Comment>
      <Position>2</Position>
      <StoredType>varchar(25)|0s</StoredType>
    </column>
    <column id="1500" parent="350" name="asin">
      <Comment>ASIN</Comment>
      <Position>3</Position>
      <StoredType>varchar(25)|0s</StoredType>
    </column>
    <column id="1501" parent="350" name="parent_asin">
      <Comment>父ASIN</Comment>
      <Position>4</Position>
      <StoredType>varchar(25)|0s</StoredType>
    </column>
    <column id="1502" parent="350" name="msku">
      <Comment>msku</Comment>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1503" parent="350" name="create_time">
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1504" parent="350" name="update_time">
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="1505" parent="350" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1506" parent="350" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1507" parent="351" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1508" parent="351" name="test_input">
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1509" parent="351" name="test_textarea">
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1510" parent="351" name="test_select">
      <Position>4</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1511" parent="351" name="test_select_multiple">
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1512" parent="351" name="test_radio">
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1513" parent="351" name="test_checkbox">
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1514" parent="351" name="test_date">
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1515" parent="351" name="test_datetime">
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1516" parent="351" name="test_user_code">
      <Position>10</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1517" parent="351" name="test_office_code">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1518" parent="351" name="test_area_code">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1519" parent="351" name="test_area_name">
      <Position>13</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1520" parent="351" name="status">
      <Position>14</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1521" parent="351" name="create_by">
      <Position>15</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1522" parent="351" name="create_date">
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1523" parent="351" name="update_by">
      <Position>17</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1524" parent="351" name="update_date">
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1525" parent="351" name="remarks">
      <Position>19</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1526" parent="352" name="id">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1527" parent="352" name="test_sort">
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1528" parent="352" name="test_data_id">
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1529" parent="352" name="test_input">
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1530" parent="352" name="test_textarea">
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1531" parent="352" name="test_select">
      <Position>6</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1532" parent="352" name="test_select_multiple">
      <Position>7</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1533" parent="352" name="test_radio">
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="1534" parent="352" name="test_checkbox">
      <Position>9</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1535" parent="352" name="test_date">
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1536" parent="352" name="test_datetime">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1537" parent="352" name="test_user_code">
      <Position>12</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1538" parent="352" name="test_office_code">
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1539" parent="352" name="test_area_code">
      <Position>14</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1540" parent="352" name="test_area_name">
      <Position>15</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1541" parent="353" name="tree_code">
      <Position>1</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1542" parent="353" name="parent_code">
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1543" parent="353" name="parent_codes">
      <Position>3</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1544" parent="353" name="tree_sort">
      <Position>4</Position>
      <StoredType>decimal(10)|0s</StoredType>
    </column>
    <column id="1545" parent="353" name="tree_sorts">
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1546" parent="353" name="tree_leaf">
      <Position>6</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1547" parent="353" name="tree_level">
      <Position>7</Position>
      <StoredType>decimal(4)|0s</StoredType>
    </column>
    <column id="1548" parent="353" name="tree_names">
      <Position>8</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="1549" parent="353" name="tree_name">
      <Position>9</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="1550" parent="353" name="status">
      <Position>10</Position>
      <StoredType>char(1)|0s</StoredType>
    </column>
    <column id="1551" parent="353" name="create_by">
      <Position>11</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1552" parent="353" name="create_date">
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1553" parent="353" name="update_by">
      <Position>13</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1554" parent="353" name="update_date">
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1555" parent="353" name="remarks">
      <Position>15</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="1556" parent="354" name="id">
      <AutoIncrement>5</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1557" parent="354" name="name">
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(32)|0s</StoredType>
    </column>
    <column id="1558" parent="354" name="app_key">
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1559" parent="354" name="app_secret">
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1560" parent="354" name="shopify_encode">
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <index id="1561" parent="354" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1562" parent="354" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1563" parent="355" name="id">
      <Position>1</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="1564" parent="355" name="key">
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="1565" parent="355" name="value">
      <Position>3</Position>
      <StoredType>varchar(256)|0s</StoredType>
    </column>
    <column id="1566" parent="355" name="remarks">
      <Position>4</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1567" parent="356" name="id">
      <AutoIncrement>2596</AutoIncrement>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="1568" parent="356" name="last_name">
      <Position>2</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1569" parent="356" name="first_name">
      <Position>3</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1570" parent="356" name="region_id">
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1571" parent="356" name="product_id">
      <Position>5</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="1572" parent="356" name="email">
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="1573" parent="356" name="serial_number">
      <Position>7</Position>
      <StoredType>varchar(40)|0s</StoredType>
    </column>
    <column id="1574" parent="356" name="buy_channel">
      <Comment>购买渠道</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1575" parent="356" name="channel_info">
      <Comment>渠道信息</Comment>
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="1576" parent="356" name="describe">
      <Position>10</Position>
      <StoredType>varchar(1024)|0s</StoredType>
    </column>
    <column id="1577" parent="356" name="create_at">
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="1578" parent="356" name="vin">
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="1579" parent="356" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1580" parent="356" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>