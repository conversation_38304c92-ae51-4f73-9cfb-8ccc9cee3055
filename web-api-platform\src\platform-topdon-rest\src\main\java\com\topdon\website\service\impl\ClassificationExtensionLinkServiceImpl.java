package com.topdon.website.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topdon.website.entity.ClassificationExtensionLink;
import com.topdon.website.mapper.ClassificationExtensionLinkMapper;
import com.topdon.website.service.ClassificationExtensionLinkService;

@Service
public class ClassificationExtensionLinkServiceImpl extends ServiceImpl<ClassificationExtensionLinkMapper, ClassificationExtensionLink> implements ClassificationExtensionLinkService{

    @Override
    public List<ClassificationExtensionLink> findByClassificationCode(String classificationCode) {
        QueryWrapper<ClassificationExtensionLink> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("classification_code", classificationCode)
                   .eq("is_display", "1") // 只查询展示的链接
                   .orderByAsc("sort_order", "id");
        return this.list(queryWrapper);
    }

}
