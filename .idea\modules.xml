<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/web-api-breeze/src/breeze-bpm/breeze-bpm.iml" filepath="$PROJECT_DIR$/web-api-breeze/src/breeze-bpm/breeze-bpm.iml" />
      <module fileurl="file://$PROJECT_DIR$/web-api-breeze/src/breeze-json/breeze-json.iml" filepath="$PROJECT_DIR$/web-api-breeze/src/breeze-json/breeze-json.iml" />
      <module fileurl="file://$PROJECT_DIR$/web-api-breeze/src/breeze-rest/breeze-rest.iml" filepath="$PROJECT_DIR$/web-api-breeze/src/breeze-rest/breeze-rest.iml" />
      <module fileurl="file://$PROJECT_DIR$/web-api-breeze/src/breeze-security-core/breeze-security-core.iml" filepath="$PROJECT_DIR$/web-api-breeze/src/breeze-security-core/breeze-security-core.iml" />
      <module fileurl="file://$PROJECT_DIR$/web-api-breeze/src/breeze-service/breeze-service.iml" filepath="$PROJECT_DIR$/web-api-breeze/src/breeze-service/breeze-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/breeze-unit-test.iml" filepath="$PROJECT_DIR$/web-api-breeze/src/breeze-unit-test/breeze-unit-test.iml" />
      <module fileurl="file://$PROJECT_DIR$/web-admin/jeesite-web.iml" filepath="$PROJECT_DIR$/web-admin/jeesite-web.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/topdon-web.iml" filepath="$PROJECT_DIR$/.idea/topdon-web.iml" />
    </modules>
  </component>
</project>