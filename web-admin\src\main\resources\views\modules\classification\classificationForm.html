<% layout('/layouts/default.html', {title: '分类管理', libs: ['validate','fileupload']}){ %>
<link rel="stylesheet" href="/admin/static/element-ui/index.css">
<script src="/admin/static/js/vue.min.js"></script>
<script src="/admin/static/element-ui/index.js"></script>
<script src="/admin/static/js/axios.min.js"></script>
<script src="/admin/static/common/axios.js"></script>
<script src="/admin/static/common/vue.js"></script>
<script src="/admin/static/common/spark-md5.min.js"></script>

<style>
	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}
	.avatar-uploader .el-upload:hover {
		border-color: #409EFF;
	}
	.el-upload__input{
		display: none !important;
	}
	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 178px;
		height: 178px;
		line-height: 178px;
		text-align: center;
	}
	.avatar {
		width: 178px;
		height: 178px;
		display: block;
	}
</style>

<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(classification.isNewRecord ? '新增分类' : '编辑分类')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${classification}" action="${ctx}/classification/classification/save" method="post" class="form-horizontal">
			<div class="box-body" id="classificationFormContent">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('上级分类')}：</label>
							<div class="col-sm-8">
								<#form:treeselect id="parent" title="${text('上级分类')}"
									path="parent.id" labelPath="parent.name"
									url="${ctx}/classification/classification/treeData?excludeCode=${classification.id}"
									class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('编码')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:hidden path="isNewRecord"/>
							<#form:input path="code" maxlength="64" readonly="${!classification.isNewRecord}" class="form-control required abc"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="256" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('当前组排序')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="treeSort" maxlength="10" class="form-control digits"/>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('导航菜单展示')}</div>
				<div class="row">
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('banner图')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="uploadBannerImage" bizKey="${classification.id}" bizType="classification_banner_image"
								uploadType="image" class="" readonly="false"  maxUploadNum="1" preview="true" returnPath="true" filePathInputId="bannerMedia"/>
								<#form:hidden path="bannerMedia"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('导航图')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="uploadMenuImage" bizKey="${classification.id}" bizType="classification_menu_image"
								uploadType="image" class="" readonly="false"  maxUploadNum="1" preview="true" returnPath="true" filePathInputId="menuMedia"/>
								<#form:hidden path="menuMedia"/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('二级品类图')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:fileupload id="uploadProImage" bizKey="${classification.id}" bizType="classification_pro_image"
								uploadType="image" class="" readonly="false"  maxUploadNum="1" preview="true" returnPath="true" filePathInputId="proMedia"/>
								<#form:hidden path="proMedia"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1" title="">
								<span class="required hide">*</span> ${text('导航文案')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-11">
								<#form:textarea path="navDesc" rows="4" maxlength="1025" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1" title="">
								<span class="required hide">*</span> ${text('二级品类文案')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-11">
								<#form:textarea path="description" rows="4" maxlength="1025" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1" title="">
								<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-11">
								<#form:textarea path="remarks" rows="4" maxlength="1024" class="form-control"/>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('拓展链接管理')}</div>
				<div class="row" style="margin-left: 100px">
					<el-table
							:data="tableData"
							border
							style="width: 100%">
						<el-table-column
								prop="iconDefault"
								label="图标(默认)"
								width="260">
							<template slot-scope="scope">
								<el-upload
										class="avatar-uploader"
										action="/admin/a/file/upload"
										:show-file-list="false"
										:with-credentials="true"
										:data="uploadImgData"
										:on-error="()=>{js.closeLoading()}"
										:on-success="(res, file)=>handleAvatarSuccess(res, file,scope.row,'iconDefault')"
										:before-upload="beforeAvatarUpload">
									<img v-if="scope.row.iconDefault" :src="scope.row.iconDefault" class="avatar">
									<i v-else class="el-icon-plus avatar-uploader-icon"></i>
								</el-upload>
							</template>
						</el-table-column>
						<el-table-column
								prop="iconHover"
								label="图标(鼠标移入)"
								width="260">
							<template slot-scope="scope">
								<el-upload
										class="avatar-uploader"
										action="/admin/a/file/upload"
										:show-file-list="false"
										:with-credentials="true"
										:data="uploadImgData"
										:on-success="(res, file)=>handleAvatarSuccess(res, file,scope.row,'iconHover')"
										:before-upload="beforeAvatarUpload">
									<img v-if="scope.row.iconHover" :src="scope.row.iconHover" class="avatar">
									<i v-else class="el-icon-plus avatar-uploader-icon"></i>
								</el-upload>
							</template>
						</el-table-column>
						<el-table-column
								prop="navText"
								label="导航文案"
								width="260">
							<template slot-scope="scope">
								<textarea class="form-control" rows="2" maxlength="50" required v-model="scope.row.navText"></textarea>
							</template>
						</el-table-column>
						<el-table-column
								prop="categoryText"
								label="二级品类文案">
							<template slot-scope="scope">
								<textarea class="form-control" rows="2" maxlength="100" required v-model="scope.row.categoryText"></textarea>
							</template>
						</el-table-column>
						<el-table-column
								prop="sortOrder"
								label="排序">
							<template slot-scope="scope">
								<input type="number" name="" class="form-control" min="0" value="" required v-model="scope.row.sortOrder">
							</template>
						</el-table-column>
						<el-table-column
								prop="isDisplay"
								label="是否展示">
							<template slot-scope="scope">
								<select name="" class="form-control" required v-model="scope.row.isDisplay">
									<option value="0">否</option>
									<option value="1">是</option>
								</select>
							</template>
						</el-table-column>
						<el-table-column
								prop="jumpLink"
								label="二级品类文案">
							<template slot-scope="scope">
								<textarea class="form-control" required v-model="scope.row.jumpLink"></textarea>
							</template>
						</el-table-column>
					</el-table>
					<div class="mt5">
						<button type="button" class="btn btn-sm btn-success" @click="addExtensionLink()">
							<i class="fa fa-plus"></i> 新增
						</button>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('classification:classification:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<script>
	var businessId = "${classification.id}"
</script>
<% } %>

<script>
var extensionLinkIndex = 0;

$("#inputForm").validate({
	submitHandler: function(form){
		// 收集拓展链接数据
		vm.collectExtensionLinksData();

		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					(contentWindow.win||contentWindow).$('#dataGrid').dataGrid('refreshTreeChildren',
							$('#parentCode').val(), '${classification.id}');
				});
			}
		}, "json");
    }
});

// 选择上级节点回调方法
function treeselectCallback(id, act, index, layero){
	if (id == 'parent' && (act == 'ok' || act == 'clear')){
		// 创建并初始化下一个节点信息，如：排序号、默认值
		$.get('${ctx}/classification/classification/createNextNode?parentCode='
				+$('#parentCode').val(), function(data){
			$('#treeSort').val(data.treeSort);
		});
	}
}



// 页面加载完成后，加载已有的拓展链接数据
$(document).ready(function() {
	var classificationCode = '${classification.code!""}';
	if (classificationCode) {
		// 加载已有的拓展链接数据
		$.get('${ctx}/classification/classificationExtensionLink/getByClassificationCode', {
			classificationCode: classificationCode
		}, function(data) {
			if (data && data.length > 0) {
				for (var i = 0; i < data.length; i++) {
					vm.addExtensionLinkData(data[i]);
				}
			}
		});
	}
});


const vm = new Vue({
	el: '#classificationFormContent',
	data: {
		tableData: [],
		uploadImgData:{}
	},
	mounted() {

	},
	methods: {
		addExtensionLink(){
			this.tableData.push({
				iconDefault:'',
				iconHover:'',
				navText:'',
				categoryText:'',
				sortOrder:'0',
				isDisplay:0,
				jumpLink:'',
			})
		},
		addExtensionLinkData(data){
			this.tableData.push(data)
		},
		handleAvatarSuccess(res, file,row,key) {
			js.closeLoading()
			console.log('handleAvatarSuccess',res, file,row,key)
			row[key] = file.response.fileUpload.fileUrl;
		},
		async beforeAvatarUpload(file) {
			js.loading()

			console.log('beforeAvatarUpload',file)
			this.uploadImgData.fileMd5=this.getFileMd5(file)
			this.uploadImgData.bizKey=businessId
			this.uploadImgData.bizType='classification_banner_image'
			this.uploadImgData.uploadType='image'
			this.uploadImgData.fileName=file.name
			this.uploadImgData.name=file.name
			return true;
		},
		async getFileMd5(file){
			await new Promise((resolve, reject)=>{
				const chunkSize = 2 * 1024 * 1024; // 2MB 每片
				const chunks = Math.ceil(file.size / chunkSize);
				let currentChunk = 0;
				const spark = new SparkMD5.ArrayBuffer();
				const fileReader = new FileReader();

				fileReader.onload = function (e) {
					spark.append(e.target.result); // 添加到 MD5 计算
					currentChunk++;
					if (currentChunk < chunks) {
						loadNext();
					} else {
						const md5 = spark.end();
						console.log('文件 MD5:', md5);
						resolve(md5)
					}
				};

				fileReader.onerror = function () {
					console.warn('读取文件失败');
					reject()
				};

				function loadNext() {
					const start = currentChunk * chunkSize;
					const end = Math.min(start + chunkSize, file.size);
					fileReader.readAsArrayBuffer(file.slice(start, end));
				}

				loadNext();
			})
		},
		collectExtensionLinksData() {
			// 将数据添加到表单中，如果已存在则更新值，否则添加新字段
			var existingInput = $('#extensionLinksJson');
			if (existingInput.length > 0) {
				// 如果已存在，更新其值
				existingInput.val(JSON.stringify(this.tableData));
			} else {
				// 如果不存在，添加新的隐藏字段
				$('#inputForm').append('<input type="hidden" id="extensionLinksJson" name="extensionLinksJson" value=\'' + JSON.stringify(this.tableData) + '\'>');
			}
		}
	}
})

</script>