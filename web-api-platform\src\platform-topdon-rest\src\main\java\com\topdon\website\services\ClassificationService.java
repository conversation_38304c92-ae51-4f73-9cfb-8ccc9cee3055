package com.topdon.website.services;

import com.hiwie.breeze.AbstractEither;
import com.hiwie.breeze.AbstractOption;
import com.hiwie.breeze.ErrorMessage;
import com.hiwie.breeze.Right;
import com.topdon.website.form.ProductQueryForm;
import com.topdon.website.model.Classification;
import com.topdon.website.model.Product;
import com.topdon.website.repositories.ClassificationRepository;
import com.topdon.website.service.ClassificationCompareService;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.Comparator;
import java.util.List;

@Named
public class ClassificationService {

    private final ClassificationRepository classificationRepository;
    private final ProductServices productService;
    private final ClassificationCompareService classificationCompareService;

    @Inject
    public ClassificationService(ClassificationRepository classificationRepository, ProductServices productService, ClassificationCompareService classificationCompareService) {
        this.classificationRepository = classificationRepository;
        this.productService = productService;
        this.classificationCompareService = classificationCompareService;
    }

    public AbstractEither<ErrorMessage, List<Classification>> list(String parentId) {
        List<Classification> list = classificationRepository.list(parentId);
        for (Classification classification : list) {
            classification.setCompare(classificationCompareService.existCompare(classification.getId()));
        }
        return Right.apply(list);
    }

    public AbstractEither<ErrorMessage, List<Classification>> menu() {
        List<Classification> classifications = classificationRepository.list("0");
        for (Classification classification : classifications) {
            classification.setCompare(classificationCompareService.existCompare(classification.getId()));
            if (classification.isHasSub()) {
                List<Classification> children = classificationRepository.list(classification.getId());
                for (Classification child : children) {
                    child.setCompare(classificationCompareService.existCompare(child.getId()));
                    if (child.getProductCount() > 0) {
                        List<Product> products = productService.list(ProductQueryForm.classForm(child.getId())).right().get();
                        child.setProducts(products);
                        child.setNewProduct(products.stream().map(Product::getNewProduct).max(Comparator.comparing(o -> o)).orElse(null));
                    }
                }
                classification.setChildren(children);
            }
        }
        return Right.apply(classifications);
    }

    public AbstractEither<ErrorMessage, Classification> get(String id) {
        AbstractOption<Classification> classificationAbstractOption = classificationRepository.get(id);
        Classification classification = classificationAbstractOption.get();
        if (classification != null) {
            classification.setCompare(classificationCompareService.existCompare(classification.getId()));
        }
        return classificationAbstractOption.toRight(Classification.Errors.NOT_FOUND);
    }


}
