<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="platform-topdon-rest:war exploded" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3d44ff61-4fa6-4936-bd35-f14419c63e9d" name="Changes" comment="Story【ID1007315】分类管理页面 图片地址">
      <change beforePath="$PROJECT_DIR$/web-api-breeze/src/breeze-bpm/breeze-bpm.iml" beforeDir="false" afterPath="$PROJECT_DIR$/web-api-breeze/src/breeze-bpm/breeze-bpm.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web-api-breeze/src/breeze-core/breeze-core.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web-api-breeze/src/breeze-import/breeze-import.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web-api-breeze/src/breeze-jdbc/breeze-jdbc.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web-api-breeze/src/breeze-repository/breeze-repository.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web-api-breeze/src/breeze-security-core/breeze-security-core.iml" beforeDir="false" afterPath="$PROJECT_DIR$/web-api-breeze/src/breeze-security-core/breeze-security-core.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web-api-breeze/src/breeze-service/breeze-service.iml" beforeDir="false" afterPath="$PROJECT_DIR$/web-api-breeze/src/breeze-service/breeze-service.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java/com/topdon/website/services/ClassificationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/web-api-platform/src/platform-topdon-rest/src/main/java/com/topdon/website/services/ClassificationService.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
        <option value="JUnit5 Test Class" />
        <option value="JUnit3 Test Class" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/web-admin" value="demo-springboot" />
        <entry key="$PROJECT_DIR$/web-api-platform" value="test" />
      </map>
    </option>
    <option name="RECENT_COMMON_BRANCH" value="test" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/web-api-platform" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="官网品类导航、品类页面新增拓展入口" />
                    <option name="lastUsedInstant" value="1753940891" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot" />
                    <option name="lastUsedInstant" value="1752475759" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="test" />
                    <option name="lastUsedInstant" value="1752220469" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/web-api-platform" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="官网品类导航、品类页面新增拓展入口" />
                    <option name="lastUsedInstant" value="1753940896" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="test" />
                    <option name="lastUsedInstant" value="1752050516" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot" />
                    <option name="lastUsedInstant" value="1751531729" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="接口更新" />
                    <option name="lastUsedInstant" value="1741579964" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot-2025-02-21-bak" />
                    <option name="lastUsedInstant" value="1740466472" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/web-api-security" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="官网品类导航、品类页面新增拓展入口" />
                    <option name="lastUsedInstant" value="1753940901" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="test" />
                    <option name="lastUsedInstant" value="1752050519" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot" />
                    <option name="lastUsedInstant" value="1751531733" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="接口更新" />
                    <option name="lastUsedInstant" value="1741579965" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot-2025-02-21-bak" />
                    <option name="lastUsedInstant" value="1740466474" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/web-api-website" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="官网品类导航、品类页面新增拓展入口" />
                    <option name="lastUsedInstant" value="1753940909" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="test" />
                    <option name="lastUsedInstant" value="1752050522" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot" />
                    <option name="lastUsedInstant" value="1751531736" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="接口更新" />
                    <option name="lastUsedInstant" value="1741579966" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot-2025-02-21-bak" />
                    <option name="lastUsedInstant" value="1740466475" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/web-api-breeze" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="官网品类导航、品类页面新增拓展入口" />
                    <option name="lastUsedInstant" value="1753940914" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="demo-springboot" />
                    <option name="lastUsedInstant" value="1752475763" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="test" />
                    <option name="lastUsedInstant" value="1752220476" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/web-admin" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="true" />
    <option name="FILTER_DEBUG" value="true" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="alwaysUpdateSnapshots" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2kXPY2AN7OxW4UeXzxrh7urmww1" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.BinaryUploader.executor": "Run",
    "Application.CartShopifyService.executor": "Run",
    "Application.CryptographyUtil.executor": "Run",
    "Application.EmailSubscribeService.executor": "Run",
    "Application.IpLookupService.executor": "Run",
    "Application.ProductLinesServiceImpl.executor": "Run",
    "Application.RequestUtil.executor": "Run",
    "Application.ShopifyGraphQLClient.executor": "Debug",
    "JavaScript Debug.informationForm.html.executor": "Run",
    "Maven.breeze-core [install].executor": "Run",
    "Maven.breeze-core [package].executor": "Run",
    "Maven.breeze-dependencies [clean].executor": "Run",
    "Maven.breeze-dependencies [install].executor": "Run",
    "Maven.breeze-dependencies [package].executor": "Run",
    "Maven.breeze-dependencies [verify].executor": "Run",
    "Maven.breeze-json [install].executor": "Run",
    "Maven.breeze-json [package].executor": "Run",
    "Maven.breeze-rest [install].executor": "Run",
    "Maven.breeze-unit-test [install].executor": "Run",
    "Maven.breeze_2.12 [clean].executor": "Run",
    "Maven.breeze_2.12 [compile].executor": "Run",
    "Maven.breeze_2.12 [install].executor": "Run",
    "Maven.breeze_2.12 [org.apache.maven.plugins:maven-deploy-plugin:3.0.0:deploy].executor": "Run",
    "Maven.breeze_2.12 [org.apache.maven.plugins:maven-install-plugin:3.1.2:install].executor": "Run",
    "Maven.breeze_2.12 [package].executor": "Run",
    "Maven.jeesite-web [clean].executor": "Run",
    "Maven.jeesite-web [package].executor": "Run",
    "Maven.platform-topdon-rest [clean].executor": "Run",
    "Maven.platform-topdon-rest [install].executor": "Run",
    "Maven.platform-topdon-rest [org.apache.maven.plugins:maven-war-plugin:3.0.0:exploded].executor": "Run",
    "Maven.platform-topdon-rest [org.apache.maven.plugins:maven-war-plugin:3.0.0:war].executor": "Run",
    "Maven.platform-topdon-rest [package].executor": "Run",
    "Maven.platform-topdon-rest [site].executor": "Run",
    "Maven.security-core [package].executor": "Run",
    "Maven.security_2.12 [clean].executor": "Run",
    "Maven.security_2.12 [install].executor": "Run",
    "Maven.security_2.12 [org.apache.maven.plugins:maven-deploy-plugin:3.0.0:deploy-file].executor": "Run",
    "Maven.security_2.12 [org.apache.maven.plugins:maven-deploy-plugin:3.0.0:deploy].executor": "Run",
    "Maven.security_2.12 [package].executor": "Run",
    "Maven.website-core [package].executor": "Run",
    "Maven.website-rest [install].executor": "Run",
    "Maven.website_2.12 [clean].executor": "Run",
    "Maven.website_2.12 [install].executor": "Run",
    "Maven.website_2.12 [org.apache.maven.plugins:maven-deploy-plugin:3.0.0:deploy-file].executor": "Run",
    "Maven.website_2.12 [package].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.AdminWebApplication.executor": "Run",
    "Spring Boot.Application.executor": "Run",
    "Spring Boot.TopdonApplication.executor": "Debug",
    "Tomcat Server.Tomcat 9.0.93.executor": "Run",
    "Tomcat Server.Tomcat.executor": "Run",
    "Tomcat Server.Unnamed.executor": "Run",
    "com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary": "JUnit3",
    "com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit3": "junit.framework.TestCase",
    "com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit5": "",
    "git-widget-placeholder": "官网品类导航、品类页面新增拓展入口",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/IdeaProjects/topdon-web/web-admin/src/main/resources/static/common",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.41034484",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "com.codeverse.userSettings.AppSettingsConfigurable",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\static\common" />
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\views\modules\menu" />
      <recent name="D:\IdeaProjects\topdon-web\web-api-platform\src\platform-topdon-rest\src\main\resources" />
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\static\js" />
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\views\modules\seller" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\mapper" />
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\static" />
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\static\ueditor\1.4" />
      <recent name="D:\IdeaProjects\topdon-web\web-admin\src\main\resources\static\js" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="junit.framework.TestCase" />
      <recent name="" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="com.topdon.website.helper" />
      <recent name="com.topdon.website.service.impl" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.topdon.admin.vo" />
      <recent name="com.topdon.admin.dto" />
      <recent name="com.topdon.admin.controller" />
      <recent name="com.jeesite.modules.email.entity" />
      <recent name="com.topdon.website.model.graphql.model" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.AdminWebApplication">
    <configuration name="CryptographyUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.hiwie.breeze.util.CryptographyUtil" />
      <module name="breeze-core" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.hiwie.breeze.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EmailSubscribeService" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.topdon.website.services.EmailSubscribeService" />
      <module name="platform-topdon-rest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.topdon.website.services.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RequestUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.topdon.website.helper.RequestUtil" />
      <module name="platform-topdon-rest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.topdon.website.helper.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ShopifyGraphQLClient" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.topdon.website.helper.ShopifyGraphQLClient" />
      <module name="platform-topdon-rest" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.topdon.website.helper.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AdminWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="jeesite-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.jeesite.modules.AdminWebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TopdonApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="dev" />
      <module name="platform-topdon-rest" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.topdon.website.TopdonApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.topdon.website.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tomcat" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 9.0.93" ALTERNATIVE_JRE_ENABLED="true" ALTERNATIVE_JRE_PATH="11">
      <option name="COMMON_VM_ARGUMENTS" value="-Dspring.profiles.active=dev" />
      <option name="UPDATING_POLICY" value="restart-server" />
      <deployment>
        <artifact name="platform-topdon-rest:war exploded">
          <settings>
            <option name="CONTEXT_PATH" value="/api" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="e720cbe6-a76f-45da-abb4-892cca144d4e" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="58218" />
      </RunnerSettings>
      <RunnerSettings RunnerId="JRebel Debug">
        <option name="DEBUG_PORT" value="58225" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="JRebel Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="JRebel Executor">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="false" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.CryptographyUtil" />
      <item itemvalue="Application.EmailSubscribeService" />
      <item itemvalue="Application.RequestUtil" />
      <item itemvalue="Application.ShopifyGraphQLClient" />
      <item itemvalue="Spring Boot.AdminWebApplication" />
      <item itemvalue="Spring Boot.TopdonApplication" />
      <item itemvalue="Tomcat Server.Tomcat" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.TopdonApplication" />
        <item itemvalue="Application.ShopifyGraphQLClient" />
        <item itemvalue="Application.RequestUtil" />
        <item itemvalue="Application.EmailSubscribeService" />
        <item itemvalue="Application.CryptographyUtil" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3d44ff61-4fa6-4936-bd35-f14419c63e9d" name="Changes" comment="" />
      <created>1723426991918</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1723426991918</updated>
      <workItem from="1723426992947" duration="2208000" />
      <workItem from="1723429219190" duration="84000" />
      <workItem from="1723429323887" duration="110000" />
      <workItem from="1723429452724" duration="95000" />
      <workItem from="1723429568033" duration="87000" />
      <workItem from="1723430043358" duration="2615000" />
      <workItem from="1723432672672" duration="287000" />
      <workItem from="1723432968766" duration="561000" />
      <workItem from="1723433537506" duration="315000" />
      <workItem from="1723433969136" duration="2201000" />
      <workItem from="1723513904391" duration="3926000" />
      <workItem from="1723521272226" duration="837000" />
      <workItem from="1723522333361" duration="554000" />
      <workItem from="1723530658482" duration="193000" />
      <workItem from="1723536871105" duration="466000" />
      <workItem from="1723598993820" duration="1041000" />
      <workItem from="1723601771326" duration="9102000" />
      <workItem from="1723621077820" duration="104000" />
      <workItem from="1723621218516" duration="5178000" />
      <workItem from="1723701698274" duration="576000" />
      <workItem from="1723712201673" duration="2380000" />
      <workItem from="1723725240879" duration="2067000" />
      <workItem from="1723774258176" duration="9345000" />
      <workItem from="1723802113358" duration="17503000" />
      <workItem from="1724148879368" duration="16791000" />
      <workItem from="1724314844409" duration="2361000" />
      <workItem from="1724317449224" duration="65000" />
      <workItem from="1724317688874" duration="86000" />
      <workItem from="1724317798320" duration="5000" />
      <workItem from="1724321824375" duration="38128000" />
      <workItem from="1724667265764" duration="14588000" />
      <workItem from="1724761028973" duration="560000" />
      <workItem from="1724761899062" duration="239000" />
      <workItem from="1724911306662" duration="55000" />
      <workItem from="1724919484117" duration="1552000" />
      <workItem from="1724924379670" duration="2240000" />
      <workItem from="1724988100339" duration="35000" />
      <workItem from="1724989010228" duration="32000" />
      <workItem from="1724989064081" duration="24000" />
      <workItem from="1724989114595" duration="40000" />
      <workItem from="1724989202579" duration="3000" />
      <workItem from="1724989431573" duration="16263000" />
      <workItem from="1725084552390" duration="29610000" />
      <workItem from="1725325980181" duration="12657000" />
      <workItem from="1725344721968" duration="99276000" />
      <workItem from="1725847317856" duration="60652000" />
      <workItem from="1726189434737" duration="16365000" />
      <workItem from="1726283641185" duration="32524000" />
      <workItem from="1726708075638" duration="217000" />
      <workItem from="1726708646645" duration="670000" />
      <workItem from="1726709860019" duration="45000" />
      <workItem from="1726711165659" duration="300000" />
      <workItem from="1726731735301" duration="850000" />
      <workItem from="1726803539736" duration="3215000" />
      <workItem from="1726906767289" duration="13661000" />
      <workItem from="1727158126832" duration="21842000" />
      <workItem from="1727573175702" duration="216000" />
      <workItem from="1727580808283" duration="32008000" />
      <workItem from="1728524047036" duration="35833000" />
      <workItem from="1728980212107" duration="4978000" />
      <workItem from="1729047994388" duration="30354000" />
      <workItem from="1729473800837" duration="19873000" />
      <workItem from="1729564617685" duration="983000" />
      <workItem from="1729596567614" duration="685000" />
      <workItem from="1729600815035" duration="2506000" />
      <workItem from="1729649066824" duration="3901000" />
      <workItem from="1729664013352" duration="220000" />
      <workItem from="1730097404408" duration="186000" />
      <workItem from="1730109034036" duration="1939000" />
      <workItem from="1730359673962" duration="2322000" />
      <workItem from="1731398486470" duration="213000" />
      <workItem from="1731399075845" duration="44000" />
      <workItem from="1731410715932" duration="74000" />
      <workItem from="1731411270960" duration="576000" />
      <workItem from="1731477681842" duration="2302000" />
      <workItem from="1731579196909" duration="1825000" />
      <workItem from="1731917099831" duration="41000" />
      <workItem from="1732073004696" duration="335000" />
      <workItem from="1732157204246" duration="11935000" />
      <workItem from="1732174589232" duration="18361000" />
      <workItem from="1732264521542" duration="143000" />
      <workItem from="1733476761205" duration="1050000" />
      <workItem from="1733733095069" duration="133000" />
      <workItem from="1733799494174" duration="2621000" />
      <workItem from="1735109762280" duration="548000" />
      <workItem from="1735866685226" duration="7681000" />
      <workItem from="1736300016335" duration="22087000" />
      <workItem from="1736385925841" duration="251000" />
      <workItem from="1736386196863" duration="51987000" />
      <workItem from="1736732906038" duration="60982000" />
      <workItem from="1736920848715" duration="15297000" />
      <workItem from="1736996844159" duration="8497000" />
      <workItem from="1737077403253" duration="13620000" />
      <workItem from="1737162697240" duration="28804000" />
      <workItem from="1737348878137" duration="77404000" />
      <workItem from="1737615022475" duration="1132000" />
      <workItem from="1737617091960" duration="13228000" />
      <workItem from="1737691334901" duration="7965000" />
      <workItem from="1738719626801" duration="4613000" />
      <workItem from="1738737361597" duration="105639000" />
      <workItem from="1739174538315" duration="66194000" />
      <workItem from="1739415663229" duration="57746000" />
      <workItem from="1739787500149" duration="9808000" />
      <workItem from="1739849655783" duration="27666000" />
      <workItem from="1739936754659" duration="92998000" />
      <workItem from="1740464638532" duration="42859000" />
      <workItem from="1740628016715" duration="1373000" />
      <workItem from="1740629406160" duration="26026000" />
      <workItem from="1740737139553" duration="49039000" />
      <workItem from="1741570544518" duration="9047000" />
      <workItem from="1741676713973" duration="3597000" />
      <workItem from="1741680325017" duration="169000" />
      <workItem from="1742263888289" duration="535000" />
      <workItem from="1742346911480" duration="13994000" />
      <workItem from="1744186645134" duration="3748000" />
      <workItem from="1744248174823" duration="926000" />
      <workItem from="1744710802122" duration="3694000" />
      <workItem from="1744869657049" duration="32459000" />
      <workItem from="1745046178475" duration="21226000" />
      <workItem from="1745221106759" duration="11000" />
      <workItem from="1745228988832" duration="1675000" />
      <workItem from="1745895083497" duration="1748000" />
      <workItem from="1746512099451" duration="3332000" />
      <workItem from="1746522578783" duration="2034000" />
      <workItem from="1746524934495" duration="2548000" />
      <workItem from="1746584143600" duration="240000" />
      <workItem from="1746585156023" duration="1134000" />
      <workItem from="1746613768625" duration="1942000" />
      <workItem from="1746673530577" duration="26000" />
      <workItem from="1746674623601" duration="891000" />
      <workItem from="1747187424670" duration="1257000" />
      <workItem from="1747191883730" duration="423000" />
      <workItem from="1747211248094" duration="6000" />
      <workItem from="1748224408437" duration="2391000" />
      <workItem from="1748255203447" duration="206000" />
      <workItem from="1748397457662" duration="1379000" />
      <workItem from="1748479898767" duration="96063000" />
      <workItem from="1749114506186" duration="25546000" />
      <workItem from="1749448316313" duration="20620000" />
      <workItem from="1749522628232" duration="417000" />
      <workItem from="1749523062622" duration="468000" />
      <workItem from="1749523553250" duration="93000" />
      <workItem from="1749526602834" duration="10805000" />
      <workItem from="1749603639374" duration="12996000" />
      <workItem from="1750147119444" duration="769000" />
      <workItem from="1750317427246" duration="323000" />
      <workItem from="1750650064523" duration="612000" />
      <workItem from="1750665117440" duration="14354000" />
      <workItem from="1750833649494" duration="23818000" />
      <workItem from="1750930163905" duration="964000" />
      <workItem from="1750931676836" duration="1260000" />
      <workItem from="1750985767048" duration="153000" />
      <workItem from="1751262948839" duration="10829000" />
      <workItem from="1751276527660" duration="164000" />
      <workItem from="1751331646216" duration="4517000" />
      <workItem from="1751352959366" duration="237000" />
      <workItem from="1751354562942" duration="290000" />
      <workItem from="1751354982555" duration="67000" />
      <workItem from="1751357302178" duration="95000" />
      <workItem from="1751421699823" duration="14674000" />
      <workItem from="1751444429768" duration="822000" />
      <workItem from="1751447774556" duration="12495000" />
      <workItem from="1751531156728" duration="1172000" />
      <workItem from="1751870375171" duration="2065000" />
      <workItem from="1751937088385" duration="2504000" />
      <workItem from="1751958012364" duration="3139000" />
      <workItem from="1752023189568" duration="12799000" />
      <workItem from="1752138550248" duration="164000" />
      <workItem from="1752142965382" duration="1500000" />
      <workItem from="1752205008100" duration="181000" />
      <workItem from="1752216218116" duration="9065000" />
      <workItem from="1752475688194" duration="194000" />
      <workItem from="1752477627570" duration="189000" />
      <workItem from="1752653120157" duration="131000" />
      <workItem from="1752723723564" duration="223000" />
      <workItem from="1752723961095" duration="491000" />
      <workItem from="1752823851813" duration="194000" />
      <workItem from="1753500033776" duration="83000" />
      <workItem from="1753934501656" duration="2765000" />
      <workItem from="1753943684251" duration="38542000" />
    </task>
    <task id="LOCAL-00183" summary="Story【ID1007129】修改经销商电话长度">
      <option name="closed" value="true" />
      <created>1746584330472</created>
      <option name="number" value="00183" />
      <option name="presentableId" value="LOCAL-00183" />
      <option name="project" value="LOCAL" />
      <updated>1746584330472</updated>
    </task>
    <task id="LOCAL-00184" summary="Story【ID1007129】修改经销商电话长度">
      <option name="closed" value="true" />
      <created>1746585189220</created>
      <option name="number" value="00184" />
      <option name="presentableId" value="LOCAL-00184" />
      <option name="project" value="LOCAL" />
      <updated>1746585189220</updated>
    </task>
    <task id="LOCAL-00185" summary="Story【ID1007129】RMA同步官网页面展示调整">
      <option name="closed" value="true" />
      <created>1746613842156</created>
      <option name="number" value="00185" />
      <option name="presentableId" value="LOCAL-00185" />
      <option name="project" value="LOCAL" />
      <updated>1746613842156</updated>
    </task>
    <task id="LOCAL-00186" summary="Story【ID1007129】 订阅管理默认时间倒叙">
      <option name="closed" value="true" />
      <created>1746674755531</created>
      <option name="number" value="00186" />
      <option name="presentableId" value="LOCAL-00186" />
      <option name="project" value="LOCAL" />
      <updated>1746674755531</updated>
    </task>
    <task id="LOCAL-00187" summary="Story【ID1007129】 订阅管理默认时间倒叙">
      <option name="closed" value="true" />
      <created>1747189383135</created>
      <option name="number" value="00187" />
      <option name="presentableId" value="LOCAL-00187" />
      <option name="project" value="LOCAL" />
      <updated>1747189383135</updated>
    </task>
    <task id="LOCAL-00188" summary="Story【ID1007175】授权卖家查询：新增授权类型">
      <option name="closed" value="true" />
      <created>1747192076294</created>
      <option name="number" value="00188" />
      <option name="presentableId" value="LOCAL-00188" />
      <option name="project" value="LOCAL" />
      <updated>1747192076295</updated>
    </task>
    <task id="LOCAL-00189" summary="Story【ID1007153】5 官网虚拟礼品卡订单履约">
      <option name="closed" value="true" />
      <created>1748224832957</created>
      <option name="number" value="00189" />
      <option name="presentableId" value="LOCAL-00189" />
      <option name="project" value="LOCAL" />
      <updated>1748224832957</updated>
    </task>
    <task id="LOCAL-00190" summary="Story【ID1007153】5 官网虚拟礼品卡订单履约">
      <option name="closed" value="true" />
      <created>1748226781486</created>
      <option name="number" value="00190" />
      <option name="presentableId" value="LOCAL-00190" />
      <option name="project" value="LOCAL" />
      <updated>1748226781486</updated>
    </task>
    <task id="LOCAL-00191" summary="Story【ID1007181】Global官网产品详情页新增购买跳转功能">
      <option name="closed" value="true" />
      <created>1748590304307</created>
      <option name="number" value="00191" />
      <option name="presentableId" value="LOCAL-00191" />
      <option name="project" value="LOCAL" />
      <updated>1748590304307</updated>
    </task>
    <task id="LOCAL-00192" summary="Story【ID1007181】筛选商品">
      <option name="closed" value="true" />
      <created>1748933462209</created>
      <option name="number" value="00192" />
      <option name="presentableId" value="LOCAL-00192" />
      <option name="project" value="LOCAL" />
      <updated>1748933462209</updated>
    </task>
    <task id="LOCAL-00193" summary="Story【ID1007181】登录接口">
      <option name="closed" value="true" />
      <created>1748943096142</created>
      <option name="number" value="00193" />
      <option name="presentableId" value="LOCAL-00193" />
      <option name="project" value="LOCAL" />
      <updated>1748943096142</updated>
    </task>
    <task id="LOCAL-00194" summary="Story【ID1007181】产品url">
      <option name="closed" value="true" />
      <created>1748943107815</created>
      <option name="number" value="00194" />
      <option name="presentableId" value="LOCAL-00194" />
      <option name="project" value="LOCAL" />
      <updated>1748943107815</updated>
    </task>
    <task id="LOCAL-00195" summary="Story【ID1007139】Zoho RMA同步官网页面展示调整">
      <option name="closed" value="true" />
      <created>1749024560629</created>
      <option name="number" value="00195" />
      <option name="presentableId" value="LOCAL-00195" />
      <option name="project" value="LOCAL" />
      <updated>1749024560629</updated>
    </task>
    <task id="LOCAL-00196" summary="Story【ID1007152】5-官网虚拟礼品卡订单履约">
      <option name="closed" value="true" />
      <created>1749177306915</created>
      <option name="number" value="00196" />
      <option name="presentableId" value="LOCAL-00196" />
      <option name="project" value="LOCAL" />
      <updated>1749177306916</updated>
    </task>
    <task id="LOCAL-00197" summary="Story【ID1007152】Global官网产品详情页新增购买跳转功能 记录ip">
      <option name="closed" value="true" />
      <created>1749463550422</created>
      <option name="number" value="00197" />
      <option name="presentableId" value="LOCAL-00197" />
      <option name="project" value="LOCAL" />
      <updated>1749463550422</updated>
    </task>
    <task id="LOCAL-00198" summary="Story【ID1007152】Global官网产品详情页新增购买跳转功能 记录ip">
      <option name="closed" value="true" />
      <created>1749463580074</created>
      <option name="number" value="00198" />
      <option name="presentableId" value="LOCAL-00198" />
      <option name="project" value="LOCAL" />
      <updated>1749463580074</updated>
    </task>
    <task id="LOCAL-00199" summary="Story【ID1007152】 菜单点击日志">
      <option name="closed" value="true" />
      <created>1749540987182</created>
      <option name="number" value="00199" />
      <option name="presentableId" value="LOCAL-00199" />
      <option name="project" value="LOCAL" />
      <updated>1749540987183</updated>
    </task>
    <task id="LOCAL-00200" summary="Story【ID1007152】 菜单点击日志导出">
      <option name="closed" value="true" />
      <created>1749541600721</created>
      <option name="number" value="00200" />
      <option name="presentableId" value="LOCAL-00200" />
      <option name="project" value="LOCAL" />
      <updated>1749541600722</updated>
    </task>
    <task id="LOCAL-00201" summary="Story【ID1007152】 订单类型">
      <option name="closed" value="true" />
      <created>1750317536222</created>
      <option name="number" value="00201" />
      <option name="presentableId" value="LOCAL-00201" />
      <option name="project" value="LOCAL" />
      <updated>1750317536223</updated>
    </task>
    <task id="LOCAL-00202" summary="Story【ID1007152】修复上传文件编码问题">
      <option name="closed" value="true" />
      <created>1750842155110</created>
      <option name="number" value="00202" />
      <option name="presentableId" value="LOCAL-00202" />
      <option name="project" value="LOCAL" />
      <updated>1750842155110</updated>
    </task>
    <task id="LOCAL-00203" summary="Story【ID1007152】修复上传文件编码问题">
      <option name="closed" value="true" />
      <created>1750842680359</created>
      <option name="number" value="00203" />
      <option name="presentableId" value="LOCAL-00203" />
      <option name="project" value="LOCAL" />
      <updated>1750842680359</updated>
    </task>
    <task id="LOCAL-00204" summary="Story【ID1007152】菜单日志添加产品名称">
      <option name="closed" value="true" />
      <created>1750930823857</created>
      <option name="number" value="00204" />
      <option name="presentableId" value="LOCAL-00204" />
      <option name="project" value="LOCAL" />
      <updated>1750930823857</updated>
    </task>
    <task id="LOCAL-00205" summary="Story【ID1007152】获取ip归属地">
      <option name="closed" value="true" />
      <created>1750932210434</created>
      <option name="number" value="00205" />
      <option name="presentableId" value="LOCAL-00205" />
      <option name="project" value="LOCAL" />
      <updated>1750932210434</updated>
    </task>
    <task id="LOCAL-00206" summary="Story【ID1007152】菜单点击日志">
      <option name="closed" value="true" />
      <created>1750932316311</created>
      <option name="number" value="00206" />
      <option name="presentableId" value="LOCAL-00206" />
      <option name="project" value="LOCAL" />
      <updated>1750932316311</updated>
    </task>
    <task id="LOCAL-00207" summary="Story【ID1007152】查询ip归属地">
      <option name="closed" value="true" />
      <created>1750932807541</created>
      <option name="number" value="00207" />
      <option name="presentableId" value="LOCAL-00207" />
      <option name="project" value="LOCAL" />
      <updated>1750932807541</updated>
    </task>
    <task id="LOCAL-00208" summary="Story【ID1007139】Zoho RMA同步官网页面展示调整">
      <option name="closed" value="true" />
      <created>1751263657032</created>
      <option name="number" value="00208" />
      <option name="presentableId" value="LOCAL-00208" />
      <option name="project" value="LOCAL" />
      <updated>1751263657032</updated>
    </task>
    <task id="LOCAL-00209" summary="Story【ID1007139】Zoho RMA同步官网页面展示调整">
      <option name="closed" value="true" />
      <created>1751268800854</created>
      <option name="number" value="00209" />
      <option name="presentableId" value="LOCAL-00209" />
      <option name="project" value="LOCAL" />
      <updated>1751268800854</updated>
    </task>
    <task id="LOCAL-00210" summary="Story【ID1007139】修复上传图片">
      <option name="closed" value="true" />
      <created>1751268969542</created>
      <option name="number" value="00210" />
      <option name="presentableId" value="LOCAL-00210" />
      <option name="project" value="LOCAL" />
      <updated>1751268969542</updated>
    </task>
    <task id="LOCAL-00211" summary="Story【ID1007139】修复上传图片">
      <option name="closed" value="true" />
      <created>1751269520033</created>
      <option name="number" value="00211" />
      <option name="presentableId" value="LOCAL-00211" />
      <option name="project" value="LOCAL" />
      <updated>1751269520033</updated>
    </task>
    <task id="LOCAL-00212" summary="Story【ID1007139】修复上传图片">
      <option name="closed" value="true" />
      <created>1751271341400</created>
      <option name="number" value="00212" />
      <option name="presentableId" value="LOCAL-00212" />
      <option name="project" value="LOCAL" />
      <updated>1751271341400</updated>
    </task>
    <task id="LOCAL-00213" summary="Story【ID1007139】修复图片展示">
      <option name="closed" value="true" />
      <created>1751273756604</created>
      <option name="number" value="00213" />
      <option name="presentableId" value="LOCAL-00213" />
      <option name="project" value="LOCAL" />
      <updated>1751273756605</updated>
    </task>
    <task id="LOCAL-00214" summary="Story【ID1007139】修复图片展示">
      <option name="closed" value="true" />
      <created>1751274429058</created>
      <option name="number" value="00214" />
      <option name="presentableId" value="LOCAL-00214" />
      <option name="project" value="LOCAL" />
      <updated>1751274429058</updated>
    </task>
    <task id="LOCAL-00215" summary="Story【ID1007139】修复获取ip">
      <option name="closed" value="true" />
      <created>1751333058550</created>
      <option name="number" value="00215" />
      <option name="presentableId" value="LOCAL-00215" />
      <option name="project" value="LOCAL" />
      <updated>1751333058550</updated>
    </task>
    <task id="LOCAL-00216" summary="Story【ID1007139】修复innerApi">
      <option name="closed" value="true" />
      <created>1751354842322</created>
      <option name="number" value="00216" />
      <option name="presentableId" value="LOCAL-00216" />
      <option name="project" value="LOCAL" />
      <updated>1751354842322</updated>
    </task>
    <task id="LOCAL-00217" summary="Story【ID1007139】修复innerApi">
      <option name="closed" value="true" />
      <created>1751355043216</created>
      <option name="number" value="00217" />
      <option name="presentableId" value="LOCAL-00217" />
      <option name="project" value="LOCAL" />
      <updated>1751355043216</updated>
    </task>
    <task id="LOCAL-00218" summary="Story【ID1007257】 产品详情页购买按钮点击日志记录优化">
      <option name="closed" value="true" />
      <created>1751452015894</created>
      <option name="number" value="00218" />
      <option name="presentableId" value="LOCAL-00218" />
      <option name="project" value="LOCAL" />
      <updated>1751452015894</updated>
    </task>
    <task id="LOCAL-00219" summary="Story【ID1007257】 产品详情页购买按钮点击日志记录优化">
      <option name="closed" value="true" />
      <created>1751531616325</created>
      <option name="number" value="00219" />
      <option name="presentableId" value="LOCAL-00219" />
      <option name="project" value="LOCAL" />
      <updated>1751531616325</updated>
    </task>
    <task id="LOCAL-00220" summary="Story【ID1007139】Zoho RMA同步官网页面展示调整 展示图片">
      <option name="closed" value="true" />
      <created>1751958214977</created>
      <option name="number" value="00220" />
      <option name="presentableId" value="LOCAL-00220" />
      <option name="project" value="LOCAL" />
      <updated>1751958214977</updated>
    </task>
    <task id="LOCAL-00221" summary="Story【ID1007252】记录折扣码复制使用情况">
      <option name="closed" value="true" />
      <created>1752050497360</created>
      <option name="number" value="00221" />
      <option name="presentableId" value="LOCAL-00221" />
      <option name="project" value="LOCAL" />
      <updated>1752050497360</updated>
    </task>
    <task id="LOCAL-00222" summary="Story【ID1007252】记录折扣码复制使用情况">
      <option name="closed" value="true" />
      <created>1752145517899</created>
      <option name="number" value="00222" />
      <option name="presentableId" value="LOCAL-00222" />
      <option name="project" value="LOCAL" />
      <updated>1752145517899</updated>
    </task>
    <task id="LOCAL-00223" summary="Story【ID1007252】记录折扣码复制使用情况 修复添加订阅邮件">
      <option name="closed" value="true" />
      <created>1752216483937</created>
      <option name="number" value="00223" />
      <option name="presentableId" value="LOCAL-00223" />
      <option name="project" value="LOCAL" />
      <updated>1752216483938</updated>
    </task>
    <task id="LOCAL-00224" summary="Story【ID1007252】记录折扣码复制使用情况">
      <option name="closed" value="true" />
      <created>1752220449922</created>
      <option name="number" value="00224" />
      <option name="presentableId" value="LOCAL-00224" />
      <option name="project" value="LOCAL" />
      <updated>1752220449922</updated>
    </task>
    <task id="LOCAL-00225" summary="Story【ID1007252】 添加zoho描述字段">
      <option name="closed" value="true" />
      <created>1752724398377</created>
      <option name="number" value="00225" />
      <option name="presentableId" value="LOCAL-00225" />
      <option name="project" value="LOCAL" />
      <updated>1752724398377</updated>
    </task>
    <task id="LOCAL-00226" summary="Story【ID1007315】官网品类导航、品类页面新增拓展入口">
      <option name="closed" value="true" />
      <created>1753941048553</created>
      <option name="number" value="00226" />
      <option name="presentableId" value="LOCAL-00226" />
      <option name="project" value="LOCAL" />
      <updated>1753941048553</updated>
    </task>
    <task id="LOCAL-00227" summary="Story【ID1007315】列表展示去掉【对比分组名称】列">
      <option name="closed" value="true" />
      <created>1753941235841</created>
      <option name="number" value="00227" />
      <option name="presentableId" value="LOCAL-00227" />
      <option name="project" value="LOCAL" />
      <updated>1753941235841</updated>
    </task>
    <task id="LOCAL-00228" summary="Story【ID1007315】分类管理页面">
      <option name="closed" value="true" />
      <created>1753954127275</created>
      <option name="number" value="00228" />
      <option name="presentableId" value="LOCAL-00228" />
      <option name="project" value="LOCAL" />
      <updated>1753954127275</updated>
    </task>
    <task id="LOCAL-00229" summary="Story【ID1007315】分类管理页面">
      <option name="closed" value="true" />
      <created>1753955716772</created>
      <option name="number" value="00229" />
      <option name="presentableId" value="LOCAL-00229" />
      <option name="project" value="LOCAL" />
      <updated>1753955716772</updated>
    </task>
    <task id="LOCAL-00230" summary="Story【ID1007315】分类管理页面 校验">
      <option name="closed" value="true" />
      <created>1753956227921</created>
      <option name="number" value="00230" />
      <option name="presentableId" value="LOCAL-00230" />
      <option name="project" value="LOCAL" />
      <updated>1753956227921</updated>
    </task>
    <task id="LOCAL-00231" summary="Story【ID1007315】分类管理页面 图片地址">
      <option name="closed" value="true" />
      <created>1753957690039</created>
      <option name="number" value="00231" />
      <option name="presentableId" value="LOCAL-00231" />
      <option name="project" value="LOCAL" />
      <updated>1753957690039</updated>
    </task>
    <option name="localTasksCounter" value="232" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Paths">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/IdeaProjects/topdon-web/web-api-breeze" />
                  <option value="dir:D:/IdeaProjects/topdon-web/web-admin" />
                  <option value="dir:D:/IdeaProjects/topdon-web/web-api-website" />
                  <option value="dir:D:/IdeaProjects/topdon-web/web-api-security" />
                  <option value="dir:D:/IdeaProjects/topdon-web/web-api-platform" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/IdeaProjects/topdon-web/web-api-website" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/IdeaProjects/topdon-web/web-admin" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="官网品类导航、品类页面新增拓展入口" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/web-admin" />
                        <option value="$PROJECT_DIR$/web-api-platform" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="Story【ID1007152】5-官网虚拟礼品卡订单履约" />
    <MESSAGE value="Story【ID1007152】Global官网产品详情页新增购买跳转功能 记录ip" />
    <MESSAGE value="Story【ID1007152】 菜单点击日志" />
    <MESSAGE value="Story【ID1007152】 菜单点击日志导出" />
    <MESSAGE value="Story【ID1007152】 订单类型" />
    <MESSAGE value="Story【ID1007152】修复上传文件编码问题" />
    <MESSAGE value="Story【ID1007152】菜单日志添加产品名称" />
    <MESSAGE value="Story【ID1007152】获取ip归属地" />
    <MESSAGE value="Story【ID1007152】菜单点击日志" />
    <MESSAGE value="Story【ID1007152】查询ip归属地" />
    <MESSAGE value="Story【ID1007139】Zoho RMA同步官网页面展示调整" />
    <MESSAGE value="Story【ID1007139】修复上传图片" />
    <MESSAGE value="Story【ID1007139】修复图片展示" />
    <MESSAGE value="Story【ID1007139】修复获取ip" />
    <MESSAGE value="Story【ID1007139】修复innerApi" />
    <MESSAGE value="Story【ID1007257】 产品详情页购买按钮点击日志记录优化" />
    <MESSAGE value="Story【ID1007139】Zoho RMA同步官网页面展示调整 展示图片" />
    <MESSAGE value="Story【ID1007252】记录折扣码复制使用情况 修复添加订阅邮件" />
    <MESSAGE value="Story【ID1007252】记录折扣码复制使用情况" />
    <MESSAGE value="Story【ID1007252】 添加zoho描述字段" />
    <MESSAGE value="Story【ID1007315】官网品类导航、品类页面新增拓展入口" />
    <MESSAGE value="Story【ID1007315】列表展示去掉【对比分组名称】列" />
    <MESSAGE value="Story【ID1007315】分类管理页面" />
    <MESSAGE value="Story【ID1007315】分类管理页面 校验" />
    <MESSAGE value="Story【ID1007315】分类管理页面 图片地址" />
    <option name="LAST_COMMIT_MESSAGE" value="Story【ID1007315】分类管理页面 图片地址" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="NON_MODAL_COMMIT_POSTPONE_SLOW_CHECKS" value="false" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/com/jeesite/jeesite-common/4.3.3-SNAPSHOT/jeesite-common-4.3.3-20220222.062520-4.jar!/com/jeesite/common/media/VideoUtils.class</url>
          <line>76</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/web-admin/src/main/java/com/jeesite/common/ueditor/upload/StorageManager.java</url>
          <line>217</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="clazz.getField(NODE_NAME_FIELD_NAME)" />
        <watch expression="clazz.getField(NODE_NAME_FIELD_NAME).get(null).toString()" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>