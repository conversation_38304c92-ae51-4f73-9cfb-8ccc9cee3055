package com.jeesite.modules.classification.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.jeesite.modules.classification.entity.ClassificationExtensionLink;

import java.util.List;

/**
 * 分类拓展链接DAO接口
 * <AUTHOR>
 * @version 2025-07-31
 */
@MyBatisDao
public interface ClassificationExtensionLinkDao extends CrudDao<ClassificationExtensionLink> {
	
	/**
	 * 根据分类编码查询拓展链接列表
	 * @param classificationCode 分类编码
	 * @return 拓展链接列表
	 */
	List<ClassificationExtensionLink> findByClassificationCode(String classificationCode);
	
	/**
	 * 根据分类编码删除拓展链接
	 * @param classificationCode 分类编码
	 * @return 删除数量
	 */
	int deleteByClassificationCode(String classificationCode);
	
}
